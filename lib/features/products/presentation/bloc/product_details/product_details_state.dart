part of 'product_details_bloc.dart';

enum ProductDetailsStatus {
  initial,
  loading,
  success,
  failure,
}

class ProductDetailsState extends Equatable {
  const ProductDetailsState({
    this.status = ProductDetailsStatus.initial,
    this.product,
    this.selectedVariant,
    this.selectedImageIndex = 0,
    this.quantity = 1,
    this.isFavorite = false,
    this.errorMessage,
  });

  final ProductDetailsStatus status;
  final Product? product;
  final ProductVariant? selectedVariant;
  final int selectedImageIndex;
  final int quantity;
  final bool isFavorite;
  final String? errorMessage;

  /// Check if currently loading
  bool get isLoading => status == ProductDetailsStatus.loading;

  /// Check if in success state
  bool get isSuccess => status == ProductDetailsStatus.success;

  /// Check if in failure state
  bool get isFailure => status == ProductDetailsStatus.failure;

  /// Check if product has variants
  bool get hasVariants => product?.hasVariants ?? false;

  /// Check if product is available for purchase
  bool get isAvailable {
    if (product == null) return false;
    
    if (selectedVariant != null) {
      return selectedVariant!.isAvailable;
    }
    
    return product!.isAvailable;
  }

  /// Get current price (variant price if selected, otherwise product price)
  String get currentPrice {
    if (selectedVariant != null) {
      return selectedVariant!.price.format();
    }
    
    return product?.price.format() ?? '';
  }

  /// Get current compare at price if on sale
  String? get currentCompareAtPrice {
    if (selectedVariant != null && selectedVariant!.isOnSale) {
      return selectedVariant!.compareAtPrice?.format();
    }
    
    if (product != null && product!.isOnSale) {
      return product!.compareAtPrice?.format();
    }
    
    return null;
  }

  /// Check if current selection is on sale
  bool get isOnSale {
    if (selectedVariant != null) {
      return selectedVariant!.isOnSale;
    }
    
    return product?.isOnSale ?? false;
  }

  /// Get discount percentage if on sale
  double get discountPercentage {
    if (selectedVariant != null) {
      return selectedVariant!.discountPercentage;
    }
    
    return product?.discountPercentage ?? 0.0;
  }

  /// Get availability message
  String get availabilityMessage {
    if (selectedVariant != null) {
      return selectedVariant!.availability.displayMessage;
    }
    
    return product?.availability.displayMessage ?? 'Unavailable';
  }

  /// Get maximum quantity that can be ordered
  int get maxQuantity {
    if (selectedVariant != null) {
      return selectedVariant!.maxOrderQuantity;
    }
    
    return product?.availability.maxAvailableQuantity ?? 0;
  }

  /// Get minimum quantity that can be ordered
  int get minQuantity {
    if (selectedVariant != null) {
      return selectedVariant!.availability.minOrderQuantity;
    }
    
    return product?.availability.minOrderQuantity ?? 1;
  }

  /// Check if current quantity is valid
  bool get isQuantityValid {
    return quantity >= minQuantity && quantity <= maxQuantity;
  }

  /// Get images to display (variant images if selected, otherwise product images)
  List<ProductImage> get displayImages {
    if (selectedVariant != null && selectedVariant!.images.isNotEmpty) {
      return selectedVariant!.images;
    }
    
    return product?.images ?? [];
  }

  /// Get currently selected image
  ProductImage? get selectedImage {
    final images = displayImages;
    if (selectedImageIndex >= 0 && selectedImageIndex < images.length) {
      return images[selectedImageIndex];
    }
    return null;
  }

  /// Get variant options for selection
  Map<String, List<String>> get variantOptions {
    if (product == null || !hasVariants) return {};
    
    final options = <String, List<String>>{};
    for (final optionName in product!.optionNames) {
      options[optionName] = product!.getOptionValues(optionName);
    }
    return options;
  }

  /// Get selected variant options
  Map<String, String> get selectedVariantOptions {
    return selectedVariant?.options ?? {};
  }

  /// Check if can add to cart
  bool get canAddToCart {
    return isAvailable && isQuantityValid && quantity > 0;
  }

  /// Get total price for current quantity
  String get totalPrice {
    if (selectedVariant != null) {
      final total = selectedVariant!.price * quantity.toDouble();
      return total.format();
    }
    
    if (product != null) {
      final total = product!.price * quantity.toDouble();
      return total.format();
    }
    
    return '';
  }

  ProductDetailsState copyWith({
    ProductDetailsStatus? status,
    Product? product,
    ProductVariant? selectedVariant,
    int? selectedImageIndex,
    int? quantity,
    bool? isFavorite,
    String? errorMessage,
  }) {
    return ProductDetailsState(
      status: status ?? this.status,
      product: product ?? this.product,
      selectedVariant: selectedVariant ?? this.selectedVariant,
      selectedImageIndex: selectedImageIndex ?? this.selectedImageIndex,
      quantity: quantity ?? this.quantity,
      isFavorite: isFavorite ?? this.isFavorite,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        product,
        selectedVariant,
        selectedImageIndex,
        quantity,
        isFavorite,
        errorMessage,
      ];

  @override
  String toString() => 'ProductDetailsState('
      'status: $status, '
      'productId: ${product?.id}, '
      'selectedVariantId: ${selectedVariant?.id}, '
      'selectedImageIndex: $selectedImageIndex, '
      'quantity: $quantity, '
      'isFavorite: $isFavorite)';
}

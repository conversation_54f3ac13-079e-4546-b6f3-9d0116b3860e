import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../shared/presentation/pages/error/error_page.dart';
import '../../shared/presentation/pages/home/<USER>';
import '../../shared/presentation/pages/splash/splash_page.dart';
import 'route_names.dart';

/// Application router configuration
class AppRouter {
  /// GoRouter instance
  static final GoRouter router = GoRouter(
    initialLocation: RouteNames.splash,
    debugLogDiagnostics: true,
    routes: [
      // Splash route
      GoRoute(
        path: RouteNames.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Home route
      GoRoute(
        path: RouteNames.home,
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),

      // Authentication routes (placeholders)
      GoRoute(
        path: RouteNames.login,
        name: 'login',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Login Page - Coming Soon')),
        ),
      ),

      GoRoute(
        path: RouteNames.register,
        name: 'register',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Register Page - Coming Soon')),
        ),
      ),

      // Product routes (placeholders)
      GoRoute(
        path: RouteNames.products,
        name: 'products',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Products Page - Coming Soon')),
        ),
      ),

      GoRoute(
        path: RouteNames.productDetails,
        name: 'product-details',
        builder: (context, state) {
          final productId = state.pathParameters['id'] ?? '';
          return Scaffold(
            body: Center(
              child: Text('Product Details Page - Product ID: $productId'),
            ),
          );
        },
      ),

      // Cart routes (placeholders)
      GoRoute(
        path: RouteNames.cart,
        name: 'cart',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Cart Page - Coming Soon')),
        ),
      ),

      // Checkout routes (placeholders)
      GoRoute(
        path: RouteNames.checkout,
        name: 'checkout',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Checkout Page - Coming Soon')),
        ),
      ),

      // Profile routes (placeholders)
      GoRoute(
        path: RouteNames.profile,
        name: 'profile',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Profile Page - Coming Soon')),
        ),
      ),

      // Settings routes (placeholders)
      GoRoute(
        path: RouteNames.settings,
        name: 'settings',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Settings Page - Coming Soon')),
        ),
      ),

      // Error route
      GoRoute(
        path: RouteNames.error,
        name: 'error',
        builder: (context, state) => ErrorPage(
          error: state.extra as String? ?? 'Unknown error occurred',
        ),
      ),
    ],

    // Error handler
    errorBuilder: (context, state) => ErrorPage(
      error: 'Page not found: ${state.uri}',
    ),

    // Redirect logic
    redirect: (context, state) {
      // TODO: Add authentication and other redirect logic here
      return null; // No redirect for now
    },
  );

  /// Navigate to a named route
  static void go(String routeName, {Map<String, String>? pathParameters, Object? extra}) {
    String path = routeName;

    // Replace path parameters if provided
    if (pathParameters != null) {
      pathParameters.forEach((key, value) {
        path = path.replaceAll(':$key', value);
      });
    }

    router.go(path, extra: extra);
  }

  /// Push a named route
  static void push(String routeName, {Map<String, String>? pathParameters, Object? extra}) {
    String path = routeName;

    // Replace path parameters if provided
    if (pathParameters != null) {
      pathParameters.forEach((key, value) {
        path = path.replaceAll(':$key', value);
      });
    }

    router.push(path, extra: extra);
  }

  /// Pop the current route
  static void pop() {
    router.pop();
  }

  /// Check if we can pop
  static bool canPop() {
    return router.canPop();
  }

  /// Navigate to home
  static void goHome() {
    go(RouteNames.home);
  }

  /// Navigate to login
  static void goLogin() {
    go(RouteNames.login);
  }

  /// Navigate to products
  static void goProducts() {
    go(RouteNames.products);
  }

  /// Navigate to product details
  static void goProductDetails(String productId) {
    go(RouteNames.productDetails, pathParameters: {'id': productId});
  }

  /// Navigate to cart
  static void goCart() {
    go(RouteNames.cart);
  }

  /// Navigate to checkout
  static void goCheckout() {
    go(RouteNames.checkout);
  }

  /// Navigate to profile
  static void goProfile() {
    go(RouteNames.profile);
  }

  /// Navigate to error page
  static void goError(String error) {
    go(RouteNames.error, extra: error);
  }
}

import 'dart:convert';
import 'package:injectable/injectable.dart';
import '../../../../core/storage/cache_manager.dart';
import '../models/product_model.dart';
import '../../domain/repositories/product_repository.dart';
import 'product_local_datasource.dart';

/// Hive-based implementation of ProductLocalDataSource
@LazySingleton(as: ProductLocalDataSource)
class HiveProductLocalDataSource implements ProductLocalDataSource {
  const HiveProductLocalDataSource(this._cacheManager);

  final CacheManager _cacheManager;

  // Cache keys
  static const String _productsKey = 'products';
  static const String _productKey = 'product';
  static const String _searchKey = 'search';
  static const String _categoryKey = 'category';
  static const String _featuredKey = 'featured';
  static const String _favoritesKey = 'favorites';
  static const String _recentlyViewedKey = 'recently_viewed';

  // Cache duration
  static const Duration _defaultCacheDuration = Duration(hours: 1);
  static const Duration _productCacheDuration = Duration(hours: 6);
  static const Duration _searchCacheDuration = Duration(minutes: 30);

  @override
  Future<void> cacheProducts(List<ProductModel> products, {String? cacheKey}) async {
    final key = cacheKey ?? _productsKey;
    final data = products.map((product) => product.toJson()).toList();
    await _cacheManager.set(key, data, duration: _defaultCacheDuration);
  }

  @override
  Future<List<ProductModel>?> getCachedProducts({String? cacheKey}) async {
    final key = cacheKey ?? _productsKey;
    final data = await _cacheManager.get<List<dynamic>>(key);
    
    if (data == null) return null;
    
    try {
      return data
          .cast<Map<String, dynamic>>()
          .map((json) => ProductModel.fromJson(json))
          .toList();
    } catch (e) {
      // Clear corrupted cache
      await _cacheManager.delete(key);
      return null;
    }
  }

  @override
  Future<void> cacheProduct(ProductModel product) async {
    final key = '$_productKey:${product.id}';
    await _cacheManager.set(key, product.toJson(), duration: _productCacheDuration);
  }

  @override
  Future<ProductModel?> getCachedProduct(String productId) async {
    final key = '$_productKey:$productId';
    final data = await _cacheManager.get<Map<String, dynamic>>(key);
    
    if (data == null) return null;
    
    try {
      return ProductModel.fromJson(data);
    } catch (e) {
      // Clear corrupted cache
      await _cacheManager.delete(key);
      return null;
    }
  }

  @override
  Future<void> cacheSearchResults({
    required String query,
    required List<ProductModel> products,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    final key = _buildSearchCacheKey(
      query: query,
      categoryId: categoryId,
      sortBy: sortBy,
      sortOrder: sortOrder,
      filter: filter,
    );
    final data = products.map((product) => product.toJson()).toList();
    await _cacheManager.set(key, data, duration: _searchCacheDuration);
  }

  @override
  Future<List<ProductModel>?> getCachedSearchResults({
    required String query,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    final key = _buildSearchCacheKey(
      query: query,
      categoryId: categoryId,
      sortBy: sortBy,
      sortOrder: sortOrder,
      filter: filter,
    );
    final data = await _cacheManager.get<List<dynamic>>(key);
    
    if (data == null) return null;
    
    try {
      return data
          .cast<Map<String, dynamic>>()
          .map((json) => ProductModel.fromJson(json))
          .toList();
    } catch (e) {
      await _cacheManager.delete(key);
      return null;
    }
  }

  @override
  Future<void> cacheCategoryProducts({
    required String categoryId,
    required List<ProductModel> products,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    final key = _buildCategoryCacheKey(
      categoryId: categoryId,
      sortBy: sortBy,
      sortOrder: sortOrder,
      filter: filter,
    );
    final data = products.map((product) => product.toJson()).toList();
    await _cacheManager.set(key, data, duration: _defaultCacheDuration);
  }

  @override
  Future<List<ProductModel>?> getCachedCategoryProducts({
    required String categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    final key = _buildCategoryCacheKey(
      categoryId: categoryId,
      sortBy: sortBy,
      sortOrder: sortOrder,
      filter: filter,
    );
    final data = await _cacheManager.get<List<dynamic>>(key);
    
    if (data == null) return null;
    
    try {
      return data
          .cast<Map<String, dynamic>>()
          .map((json) => ProductModel.fromJson(json))
          .toList();
    } catch (e) {
      await _cacheManager.delete(key);
      return null;
    }
  }

  @override
  Future<void> cacheFeaturedProducts(List<ProductModel> products) async {
    final data = products.map((product) => product.toJson()).toList();
    await _cacheManager.set(_featuredKey, data, duration: _defaultCacheDuration);
  }

  @override
  Future<List<ProductModel>?> getCachedFeaturedProducts() async {
    final data = await _cacheManager.get<List<dynamic>>(_featuredKey);
    
    if (data == null) return null;
    
    try {
      return data
          .cast<Map<String, dynamic>>()
          .map((json) => ProductModel.fromJson(json))
          .toList();
    } catch (e) {
      await _cacheManager.delete(_featuredKey);
      return null;
    }
  }

  @override
  Future<void> addToFavorites(String productId) async {
    final favorites = await getFavoriteProductIds();
    if (!favorites.contains(productId)) {
      favorites.add(productId);
      await _cacheManager.set(_favoritesKey, favorites);
    }
  }

  @override
  Future<void> removeFromFavorites(String productId) async {
    final favorites = await getFavoriteProductIds();
    favorites.remove(productId);
    await _cacheManager.set(_favoritesKey, favorites);
  }

  @override
  Future<List<String>> getFavoriteProductIds() async {
    final data = await _cacheManager.get<List<dynamic>>(_favoritesKey);
    return data?.cast<String>() ?? [];
  }

  @override
  Future<bool> isProductFavorite(String productId) async {
    final favorites = await getFavoriteProductIds();
    return favorites.contains(productId);
  }

  @override
  Future<void> addToRecentlyViewed(String productId) async {
    final recentlyViewed = await getRecentlyViewedProductIds();
    
    // Remove if already exists to move to front
    recentlyViewed.remove(productId);
    
    // Add to front
    recentlyViewed.insert(0, productId);
    
    // Keep only last 50 items
    if (recentlyViewed.length > 50) {
      recentlyViewed.removeRange(50, recentlyViewed.length);
    }
    
    await _cacheManager.set(_recentlyViewedKey, recentlyViewed);
  }

  @override
  Future<List<String>> getRecentlyViewedProductIds({int limit = 10}) async {
    final data = await _cacheManager.get<List<dynamic>>(_recentlyViewedKey);
    final recentlyViewed = data?.cast<String>() ?? [];
    
    return recentlyViewed.take(limit).toList();
  }

  @override
  Future<void> clearRecentlyViewed() async {
    await _cacheManager.delete(_recentlyViewedKey);
  }

  @override
  Future<void> clearCache() async {
    await _cacheManager.clear();
  }

  @override
  Future<void> clearExpiredCache() async {
    // Hive cache manager should handle this automatically
    // This is a placeholder for manual cleanup if needed
  }

  @override
  Future<bool> isCacheValid(String cacheKey, {Duration? maxAge}) async {
    return await _cacheManager.exists(cacheKey);
  }

  /// Build cache key for search results
  String _buildSearchCacheKey({
    required String query,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) {
    final keyParts = [
      _searchKey,
      query.toLowerCase().trim(),
      categoryId ?? 'all',
      sortBy?.name ?? 'default',
      sortOrder.name,
    ];
    
    if (filter != null && !filter.isEmpty) {
      final filterHash = _hashFilter(filter);
      keyParts.add(filterHash);
    }
    
    return keyParts.join(':');
  }

  /// Build cache key for category products
  String _buildCategoryCacheKey({
    required String categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) {
    final keyParts = [
      _categoryKey,
      categoryId,
      sortBy?.name ?? 'default',
      sortOrder.name,
    ];
    
    if (filter != null && !filter.isEmpty) {
      final filterHash = _hashFilter(filter);
      keyParts.add(filterHash);
    }
    
    return keyParts.join(':');
  }

  /// Create a hash for filter to use in cache keys
  String _hashFilter(ProductFilter filter) {
    final filterMap = filter.toMap();
    final filterString = json.encode(filterMap);
    return filterString.hashCode.toString();
  }
}

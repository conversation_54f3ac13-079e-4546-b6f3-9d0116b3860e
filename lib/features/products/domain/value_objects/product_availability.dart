import 'package:equatable/equatable.dart';

/// Enumeration for product availability status
enum AvailabilityStatus {
  inStock,
  outOfStock,
  lowStock,
  preOrder,
  discontinued,
  backOrder,
}

/// Value object representing product availability
class ProductAvailability extends Equatable {
  const ProductAvailability({
    required this.status,
    required this.quantity,
    this.lowStockThreshold = 5,
    this.allowBackorder = false,
    this.estimatedRestockDate,
    this.maxOrderQuantity,
    this.minOrderQuantity = 1,
  });

  final AvailabilityStatus status;
  final int quantity;
  final int lowStockThreshold;
  final bool allowBackorder;
  final DateTime? estimatedRestockDate;
  final int? maxOrderQuantity;
  final int minOrderQuantity;

  /// Create availability for in-stock product
  factory ProductAvailability.inStock({
    required int quantity,
    int lowStockThreshold = 5,
    int? maxOrderQuantity,
    int minOrderQuantity = 1,
  }) {
    final status = quantity <= lowStockThreshold 
        ? AvailabilityStatus.lowStock 
        : AvailabilityStatus.inStock;
    
    return ProductAvailability(
      status: status,
      quantity: quantity,
      lowStockThreshold: lowStockThreshold,
      maxOrderQuantity: maxOrderQuantity,
      minOrderQuantity: minOrderQuantity,
    );
  }

  /// Create availability for out-of-stock product
  factory ProductAvailability.outOfStock({
    bool allowBackorder = false,
    DateTime? estimatedRestockDate,
  }) {
    return ProductAvailability(
      status: AvailabilityStatus.outOfStock,
      quantity: 0,
      allowBackorder: allowBackorder,
      estimatedRestockDate: estimatedRestockDate,
    );
  }

  /// Create availability for pre-order product
  factory ProductAvailability.preOrder({
    DateTime? estimatedRestockDate,
    int? maxOrderQuantity,
    int minOrderQuantity = 1,
  }) {
    return ProductAvailability(
      status: AvailabilityStatus.preOrder,
      quantity: 0,
      estimatedRestockDate: estimatedRestockDate,
      maxOrderQuantity: maxOrderQuantity,
      minOrderQuantity: minOrderQuantity,
    );
  }

  /// Create availability for discontinued product
  factory ProductAvailability.discontinued() {
    return const ProductAvailability(
      status: AvailabilityStatus.discontinued,
      quantity: 0,
    );
  }

  /// Check if product is available for purchase
  bool get isAvailable {
    switch (status) {
      case AvailabilityStatus.inStock:
      case AvailabilityStatus.lowStock:
        return quantity > 0;
      case AvailabilityStatus.preOrder:
        return true;
      case AvailabilityStatus.backOrder:
        return allowBackorder;
      case AvailabilityStatus.outOfStock:
      case AvailabilityStatus.discontinued:
        return false;
    }
  }

  /// Check if product is in stock
  bool get isInStock => status == AvailabilityStatus.inStock && quantity > 0;

  /// Check if product is out of stock
  bool get isOutOfStock => status == AvailabilityStatus.outOfStock || quantity == 0;

  /// Check if product is low in stock
  bool get isLowStock => status == AvailabilityStatus.lowStock;

  /// Check if product can be backordered
  bool get canBackorder => allowBackorder && status == AvailabilityStatus.outOfStock;

  /// Check if product is available for pre-order
  bool get isPreOrder => status == AvailabilityStatus.preOrder;

  /// Check if product is discontinued
  bool get isDiscontinued => status == AvailabilityStatus.discontinued;

  /// Get maximum quantity that can be ordered
  int get maxAvailableQuantity {
    if (!isAvailable) return 0;
    
    if (maxOrderQuantity != null) {
      return quantity > 0 
          ? quantity.clamp(0, maxOrderQuantity!)
          : maxOrderQuantity!;
    }
    
    return quantity;
  }

  /// Check if a specific quantity can be ordered
  bool canOrderQuantity(int requestedQuantity) {
    if (!isAvailable) return false;
    if (requestedQuantity < minOrderQuantity) return false;
    if (maxOrderQuantity != null && requestedQuantity > maxOrderQuantity!) return false;
    
    switch (status) {
      case AvailabilityStatus.inStock:
      case AvailabilityStatus.lowStock:
        return requestedQuantity <= quantity;
      case AvailabilityStatus.preOrder:
      case AvailabilityStatus.backOrder:
        return true;
      case AvailabilityStatus.outOfStock:
        return allowBackorder;
      case AvailabilityStatus.discontinued:
        return false;
    }
  }

  /// Get availability message for display
  String get displayMessage {
    switch (status) {
      case AvailabilityStatus.inStock:
        return 'In Stock ($quantity available)';
      case AvailabilityStatus.lowStock:
        return 'Low Stock ($quantity remaining)';
      case AvailabilityStatus.outOfStock:
        if (allowBackorder) {
          return 'Out of Stock - Backorder Available';
        }
        if (estimatedRestockDate != null) {
          return 'Out of Stock - Restocking ${estimatedRestockDate!.toLocal().toString().split(' ')[0]}';
        }
        return 'Out of Stock';
      case AvailabilityStatus.preOrder:
        if (estimatedRestockDate != null) {
          return 'Pre-Order - Available ${estimatedRestockDate!.toLocal().toString().split(' ')[0]}';
        }
        return 'Pre-Order Available';
      case AvailabilityStatus.backOrder:
        return 'Backorder';
      case AvailabilityStatus.discontinued:
        return 'Discontinued';
    }
  }

  /// Create a copy with modified values
  ProductAvailability copyWith({
    AvailabilityStatus? status,
    int? quantity,
    int? lowStockThreshold,
    bool? allowBackorder,
    DateTime? estimatedRestockDate,
    int? maxOrderQuantity,
    int? minOrderQuantity,
  }) {
    return ProductAvailability(
      status: status ?? this.status,
      quantity: quantity ?? this.quantity,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      allowBackorder: allowBackorder ?? this.allowBackorder,
      estimatedRestockDate: estimatedRestockDate ?? this.estimatedRestockDate,
      maxOrderQuantity: maxOrderQuantity ?? this.maxOrderQuantity,
      minOrderQuantity: minOrderQuantity ?? this.minOrderQuantity,
    );
  }

  @override
  List<Object?> get props => [
        status,
        quantity,
        lowStockThreshold,
        allowBackorder,
        estimatedRestockDate,
        maxOrderQuantity,
        minOrderQuantity,
      ];

  @override
  String toString() => 'ProductAvailability(status: $status, quantity: $quantity)';
}

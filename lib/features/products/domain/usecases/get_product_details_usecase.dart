import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../entities/product.dart';
import '../repositories/product_repository.dart';

/// Use case for getting detailed product information
class GetProductDetailsUseCase implements UseCase<Product, GetProductDetailsParams> {
  const GetProductDetailsUseCase(this._repository);

  final ProductRepository _repository;

  @override
  Future<Either<Failure, Product>> call(GetProductDetailsParams params) async {
    // Get the product details
    final productResult = await _repository.getProduct(params.productId);
    
    if (productResult.isLeft()) {
      return productResult;
    }

    final product = productResult.getOrElse(() => throw Exception('Product not found'));

    // Add to recently viewed if requested
    if (params.addToRecentlyViewed) {
      await _repository.addToRecentlyViewed(params.productId);
    }

    return Right(product);
  }
}

/// Parameters for getting product details
class GetProductDetailsParams extends UseCaseParams {
  const GetProductDetailsParams({
    required this.productId,
    this.addToRecentlyViewed = true,
  });

  final String productId;
  final bool addToRecentlyViewed;

  @override
  String toString() => 'GetProductDetailsParams('
      'productId: $productId, '
      'addToRecentlyViewed: $addToRecentlyViewed)';
}

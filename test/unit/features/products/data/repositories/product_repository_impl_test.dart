import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:loc_store/core/errors/exceptions.dart';
import 'package:loc_store/core/errors/failures.dart';
import 'package:loc_store/core/network/network_info.dart';
import 'package:loc_store/features/products/data/datasources/product_local_datasource.dart';
import 'package:loc_store/features/products/data/datasources/product_remote_datasource.dart';
import 'package:loc_store/features/products/data/models/product_model.dart';
import 'package:loc_store/features/products/data/repositories/product_repository_impl.dart';
import 'package:loc_store/features/products/domain/entities/product.dart';
import 'package:loc_store/features/products/domain/repositories/product_repository.dart';
import 'package:loc_store/features/products/domain/value_objects/money.dart';
import 'package:loc_store/features/products/domain/value_objects/product_availability.dart';
import 'package:loc_store/features/products/domain/value_objects/product_category.dart';

class MockProductRemoteDataSource extends Mock implements ProductRemoteDataSource {}
class MockProductLocalDataSource extends Mock implements ProductLocalDataSource {}
class MockNetworkInfo extends Mock implements NetworkInfo {}

void main() {
  late ProductRepositoryImpl repository;
  late MockProductRemoteDataSource mockRemoteDataSource;
  late MockProductLocalDataSource mockLocalDataSource;
  late MockNetworkInfo mockNetworkInfo;

  setUp(() {
    mockRemoteDataSource = MockProductRemoteDataSource();
    mockLocalDataSource = MockProductLocalDataSource();
    mockNetworkInfo = MockNetworkInfo();
    repository = ProductRepositoryImpl(
      remoteDataSource: mockRemoteDataSource,
      localDataSource: mockLocalDataSource,
      networkInfo: mockNetworkInfo,
    );
  });

  group('getProducts', () {
    final testProductModels = [
      const ProductModel(
        id: '1',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        name: 'Test Product 1',
        description: 'Description 1',
        price: 10.0,
        currency: 'USD',
        categoryId: 'cat1',
        categoryName: 'Electronics',
        categorySlug: 'electronics',
        availabilityStatus: 'in_stock',
        availabilityQuantity: 10,
        images: [],
      ),
      const ProductModel(
        id: '2',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        name: 'Test Product 2',
        description: 'Description 2',
        price: 20.0,
        currency: 'USD',
        categoryId: 'cat1',
        categoryName: 'Electronics',
        categorySlug: 'electronics',
        availabilityStatus: 'in_stock',
        availabilityQuantity: 5,
        images: [],
      ),
    ];

    test('should return products from remote when network is connected', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockRemoteDataSource.getProducts()).thenAnswer((_) async => testProductModels);
      when(() => mockLocalDataSource.cacheProducts(any(), cacheKey: any(named: 'cacheKey')))
          .thenAnswer((_) async {});

      // act
      final result = await repository.getProducts();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Expected Right but got Left: $failure'),
        (products) {
          expect(products, hasLength(2));
          expect(products.first.name, 'Test Product 1');
          expect(products.last.name, 'Test Product 2');
        },
      );

      verify(() => mockRemoteDataSource.getProducts()).called(1);
      verify(() => mockLocalDataSource.cacheProducts(any(), cacheKey: any(named: 'cacheKey'))).called(1);
    });

    test('should return cached products when network is not connected', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      when(() => mockLocalDataSource.getCachedProducts(cacheKey: any(named: 'cacheKey')))
          .thenAnswer((_) async => testProductModels);

      // act
      final result = await repository.getProducts();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Expected Right but got Left: $failure'),
        (products) {
          expect(products, hasLength(2));
          expect(products.first.name, 'Test Product 1');
        },
      );

      verify(() => mockLocalDataSource.getCachedProducts(cacheKey: any(named: 'cacheKey'))).called(1);
      verifyNever(() => mockRemoteDataSource.getProducts());
    });

    test('should return NetworkFailure when no network and no cache', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      when(() => mockLocalDataSource.getCachedProducts(cacheKey: any(named: 'cacheKey')))
          .thenAnswer((_) async => null);

      // act
      final result = await repository.getProducts();

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure, isA<NetworkFailure>()),
        (products) => fail('Expected Left but got Right: $products'),
      );
    });

    test('should return ServerFailure when remote throws ServerException', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockRemoteDataSource.getProducts())
          .thenThrow(const ServerException(message: 'Server error'));

      // act
      final result = await repository.getProducts();

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure, isA<ServerFailure>()),
        (products) => fail('Expected Left but got Right: $products'),
      );
    });
  });

  group('getProduct', () {
    const testProductModel = ProductModel(
      id: '1',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
      name: 'Test Product',
      description: 'Description',
      price: 10.0,
      currency: 'USD',
      categoryId: 'cat1',
      categoryName: 'Electronics',
      categorySlug: 'electronics',
      availabilityStatus: 'in_stock',
      availabilityQuantity: 10,
      images: [],
    );

    test('should return product from remote when network is connected', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockRemoteDataSource.getProduct('1')).thenAnswer((_) async => testProductModel);
      when(() => mockLocalDataSource.cacheProduct(any())).thenAnswer((_) async {});
      when(() => mockLocalDataSource.getCachedProduct('1')).thenAnswer((_) async => null);

      // act
      final result = await repository.getProduct('1');

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Expected Right but got Left: $failure'),
        (product) {
          expect(product.id, '1');
          expect(product.name, 'Test Product');
        },
      );

      verify(() => mockRemoteDataSource.getProduct('1')).called(1);
      verify(() => mockLocalDataSource.cacheProduct(any())).called(1);
    });

    test('should return cached product when network is not connected', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      when(() => mockLocalDataSource.getCachedProduct('1')).thenAnswer((_) async => testProductModel);

      // act
      final result = await repository.getProduct('1');

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Expected Right but got Left: $failure'),
        (product) {
          expect(product.id, '1');
          expect(product.name, 'Test Product');
        },
      );

      verify(() => mockLocalDataSource.getCachedProduct('1')).called(1);
      verifyNever(() => mockRemoteDataSource.getProduct('1'));
    });

    test('should return cached product when remote fails but cache exists', () async {
      // arrange
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(() => mockLocalDataSource.getCachedProduct('1')).thenAnswer((_) async => testProductModel);
      when(() => mockRemoteDataSource.getProduct('1'))
          .thenThrow(const ServerException(message: 'Server error'));

      // act
      final result = await repository.getProduct('1');

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Expected Right but got Left: $failure'),
        (product) {
          expect(product.id, '1');
          expect(product.name, 'Test Product');
        },
      );

      verify(() => mockLocalDataSource.getCachedProduct('1')).called(1);
      verify(() => mockRemoteDataSource.getProduct('1')).called(1);
    });
  });

  group('addToFavorites', () {
    test('should add product to favorites successfully', () async {
      // arrange
      when(() => mockLocalDataSource.addToFavorites('1')).thenAnswer((_) async {});

      // act
      final result = await repository.addToFavorites('1');

      // assert
      expect(result.isRight(), true);
      verify(() => mockLocalDataSource.addToFavorites('1')).called(1);
    });

    test('should return CacheFailure when local datasource throws CacheException', () async {
      // arrange
      when(() => mockLocalDataSource.addToFavorites('1'))
          .thenThrow(const CacheException(message: 'Cache error'));

      // act
      final result = await repository.addToFavorites('1');

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure, isA<CacheFailure>()),
        (success) => fail('Expected Left but got Right'),
      );
    });
  });

  group('isProductFavorite', () {
    test('should return true when product is favorite', () async {
      // arrange
      when(() => mockLocalDataSource.isProductFavorite('1')).thenAnswer((_) async => true);

      // act
      final result = await repository.isProductFavorite('1');

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Expected Right but got Left: $failure'),
        (isFavorite) => expect(isFavorite, true),
      );
    });

    test('should return false when product is not favorite', () async {
      // arrange
      when(() => mockLocalDataSource.isProductFavorite('1')).thenAnswer((_) async => false);

      // act
      final result = await repository.isProductFavorite('1');

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Expected Right but got Left: $failure'),
        (isFavorite) => expect(isFavorite, false),
      );
    });
  });
}

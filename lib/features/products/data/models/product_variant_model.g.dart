// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_variant_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductVariantModel _$ProductVariantModelFromJson(Map<String, dynamic> json) =>
    ProductVariantModel(
      id: json['id'] as String,
      productId: json['product_id'] as String,
      title: json['title'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      availabilityStatus: json['availability_status'] as String,
      availabilityQuantity: (json['availability_quantity'] as num).toInt(),
      options: Map<String, String>.from(json['options'] as Map),
      sku: json['sku'] as String?,
      barcode: json['barcode'] as String?,
      compareAtPrice: (json['compare_at_price'] as num?)?.toDouble(),
      weight: (json['weight'] as num?)?.toDouble(),
      images: (json['images'] as List<dynamic>?)
              ?.map(
                  (e) => ProductImageModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      position: (json['position'] as num?)?.toInt() ?? 0,
      requiresShipping: json['requires_shipping'] as bool? ?? true,
      taxable: json['taxable'] as bool? ?? true,
      inventoryPolicy: json['inventory_policy'] as String? ?? 'deny',
      fulfillmentService: json['fulfillment_service'] as String? ?? 'manual',
      lowStockThreshold: (json['low_stock_threshold'] as num?)?.toInt() ?? 5,
      allowBackorder: json['allow_backorder'] as bool? ?? false,
      estimatedRestockDate: json['estimated_restock_date'] == null
          ? null
          : DateTime.parse(json['estimated_restock_date'] as String),
      maxOrderQuantity: (json['max_order_quantity'] as num?)?.toInt(),
      minOrderQuantity: (json['min_order_quantity'] as num?)?.toInt() ?? 1,
    );

Map<String, dynamic> _$ProductVariantModelToJson(
        ProductVariantModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'product_id': instance.productId,
      'title': instance.title,
      'sku': instance.sku,
      'barcode': instance.barcode,
      'price': instance.price,
      'currency': instance.currency,
      'compare_at_price': instance.compareAtPrice,
      'availability_status': instance.availabilityStatus,
      'availability_quantity': instance.availabilityQuantity,
      'low_stock_threshold': instance.lowStockThreshold,
      'allow_backorder': instance.allowBackorder,
      'estimated_restock_date':
          instance.estimatedRestockDate?.toIso8601String(),
      'max_order_quantity': instance.maxOrderQuantity,
      'min_order_quantity': instance.minOrderQuantity,
      'options': instance.options,
      'weight': instance.weight,
      'images': instance.images.map((e) => e.toJson()).toList(),
      'position': instance.position,
      'requires_shipping': instance.requiresShipping,
      'taxable': instance.taxable,
      'inventory_policy': instance.inventoryPolicy,
      'fulfillment_service': instance.fulfillmentService,
    };

import '../models/product_model.dart';
import '../../domain/repositories/product_repository.dart';

/// Abstract interface for product local data source (caching)
abstract class ProductLocalDataSource {
  /// Cache products
  Future<void> cacheProducts(List<ProductModel> products, {String? cacheKey});

  /// Get cached products
  Future<List<ProductModel>?> getCachedProducts({String? cacheKey});

  /// Cache a single product
  Future<void> cacheProduct(ProductModel product);

  /// Get cached product by ID
  Future<ProductModel?> getCachedProduct(String productId);

  /// Cache search results
  Future<void> cacheSearchResults({
    required String query,
    required List<ProductModel> products,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Get cached search results
  Future<List<ProductModel>?> getCachedSearchResults({
    required String query,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Cache category products
  Future<void> cacheCategoryProducts({
    required String categoryId,
    required List<ProductModel> products,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Get cached category products
  Future<List<ProductModel>?> getCachedCategoryProducts({
    required String categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Cache featured products
  Future<void> cacheFeaturedProducts(List<ProductModel> products);

  /// Get cached featured products
  Future<List<ProductModel>?> getCachedFeaturedProducts();

  /// Add product to favorites
  Future<void> addToFavorites(String productId);

  /// Remove product from favorites
  Future<void> removeFromFavorites(String productId);

  /// Get favorite product IDs
  Future<List<String>> getFavoriteProductIds();

  /// Check if product is favorite
  Future<bool> isProductFavorite(String productId);

  /// Add product to recently viewed
  Future<void> addToRecentlyViewed(String productId);

  /// Get recently viewed product IDs
  Future<List<String>> getRecentlyViewedProductIds({int limit = 10});

  /// Clear recently viewed products
  Future<void> clearRecentlyViewed();

  /// Clear all cached products
  Future<void> clearCache();

  /// Clear expired cache entries
  Future<void> clearExpiredCache();

  /// Check if cache is valid for a given key
  Future<bool> isCacheValid(String cacheKey, {Duration? maxAge});
}

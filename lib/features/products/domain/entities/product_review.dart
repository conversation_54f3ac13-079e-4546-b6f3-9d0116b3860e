import 'package:equatable/equatable.dart';
import '../../../../shared/domain/entities/base_entity.dart';

/// Entity representing a product review
class ProductReview extends BaseEntity {
  const ProductReview({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    required this.productId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.title,
    required this.content,
    this.userEmail,
    this.userAvatar,
    this.isVerifiedPurchase = false,
    this.isApproved = true,
    this.helpfulCount = 0,
    this.notHelpfulCount = 0,
    this.images = const [],
    this.variantId,
    this.variantTitle,
  });

  final String productId;
  final String userId;
  final String userName;
  final String? userEmail;
  final String? userAvatar;
  final int rating; // 1-5 stars
  final String title;
  final String content;
  final bool isVerifiedPurchase;
  final bool isApproved;
  final int helpfulCount;
  final int notHelpfulCount;
  final List<String> images;
  final String? variantId;
  final String? variantTitle;

  /// Check if review has a good rating (4-5 stars)
  bool get isPositive => rating >= 4;

  /// Check if review has a bad rating (1-2 stars)
  bool get isNegative => rating <= 2;

  /// Check if review is neutral (3 stars)
  bool get isNeutral => rating == 3;

  /// Check if review has images
  bool get hasImages => images.isNotEmpty;

  /// Check if review is for a specific variant
  bool get isVariantReview => variantId != null;

  /// Get helpfulness ratio (helpful / total votes)
  double get helpfulnessRatio {
    final totalVotes = helpfulCount + notHelpfulCount;
    if (totalVotes == 0) return 0.0;
    return helpfulCount / totalVotes;
  }

  /// Check if review is considered helpful (>60% helpful votes)
  bool get isHelpful => helpfulnessRatio > 0.6 && (helpfulCount + notHelpfulCount) >= 3;

  /// Get star rating as string
  String get starRating => '★' * rating + '☆' * (5 - rating);

  /// Get formatted rating text
  String get ratingText {
    switch (rating) {
      case 1:
        return 'Poor';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Very Good';
      case 5:
        return 'Excellent';
      default:
        return 'Unknown';
    }
  }

  /// Get review summary for display
  String get summary {
    final buffer = StringBuffer();
    buffer.write('$rating/5 stars');
    
    if (isVerifiedPurchase) {
      buffer.write(' • Verified Purchase');
    }
    
    if (isVariantReview && variantTitle != null) {
      buffer.write(' • $variantTitle');
    }
    
    return buffer.toString();
  }

  /// Create a copy with modified values
  ProductReview copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? productId,
    String? userId,
    String? userName,
    String? userEmail,
    String? userAvatar,
    int? rating,
    String? title,
    String? content,
    bool? isVerifiedPurchase,
    bool? isApproved,
    int? helpfulCount,
    int? notHelpfulCount,
    List<String>? images,
    String? variantId,
    String? variantTitle,
  }) {
    return ProductReview(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      productId: productId ?? this.productId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      userAvatar: userAvatar ?? this.userAvatar,
      rating: rating ?? this.rating,
      title: title ?? this.title,
      content: content ?? this.content,
      isVerifiedPurchase: isVerifiedPurchase ?? this.isVerifiedPurchase,
      isApproved: isApproved ?? this.isApproved,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      notHelpfulCount: notHelpfulCount ?? this.notHelpfulCount,
      images: images ?? this.images,
      variantId: variantId ?? this.variantId,
      variantTitle: variantTitle ?? this.variantTitle,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        productId,
        userId,
        userName,
        userEmail,
        userAvatar,
        rating,
        title,
        content,
        isVerifiedPurchase,
        isApproved,
        helpfulCount,
        notHelpfulCount,
        images,
        variantId,
        variantTitle,
      ];

  @override
  String toString() => 'ProductReview(id: $id, productId: $productId, rating: $rating, title: $title)';
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import '../../../domain/entities/product.dart';
import '../../../domain/entities/product_image.dart';
import '../../../domain/entities/product_variant.dart';
import '../../../domain/usecases/get_product_details_usecase.dart';
import '../../../domain/usecases/manage_favorites_usecase.dart';

part 'product_details_event.dart';
part 'product_details_state.dart';

@injectable
class ProductDetailsBloc extends Bloc<ProductDetailsEvent, ProductDetailsState> {
  ProductDetailsBloc({
    required this.getProductDetailsUseCase,
    required this.addToFavoritesUseCase,
    required this.removeFromFavoritesUseCase,
    required this.isProductFavoriteUseCase,
  }) : super(const ProductDetailsState()) {
    on<ProductDetailsRequested>(_onProductDetailsRequested);
    on<ProductVariantSelected>(_onProductVariantSelected);
    on<ProductQuantityChanged>(_onProductQuantityChanged);
    on<ProductImageSelected>(_onProductImageSelected);
    on<ProductFavoriteToggled>(_onProductFavoriteToggled);
    on<ProductDetailsRefreshed>(_onProductDetailsRefreshed);
  }

  final GetProductDetailsUseCase getProductDetailsUseCase;
  final AddToFavoritesUseCase addToFavoritesUseCase;
  final RemoveFromFavoritesUseCase removeFromFavoritesUseCase;
  final IsProductFavoriteUseCase isProductFavoriteUseCase;

  Future<void> _onProductDetailsRequested(
    ProductDetailsRequested event,
    Emitter<ProductDetailsState> emit,
  ) async {
    emit(state.copyWith(status: ProductDetailsStatus.loading));

    final result = await getProductDetailsUseCase(GetProductDetailsParams(
      productId: event.productId,
      addToRecentlyViewed: true,
    ));

    await result.fold(
      (failure) async {
        emit(state.copyWith(
          status: ProductDetailsStatus.failure,
          errorMessage: failure.message,
        ));
      },
      (product) async {
        // Check if product is favorite
        final isFavoriteResult = await isProductFavoriteUseCase(
          IsProductFavoriteParams(productId: product.id),
        );

        final isFavorite = isFavoriteResult.fold(
          (failure) => false,
          (favorite) => favorite,
        );

        // Select first available variant if product has variants
        ProductVariant? selectedVariant;
        if (product.hasVariants && product.availableVariants.isNotEmpty) {
          selectedVariant = product.availableVariants.first;
        }

        emit(state.copyWith(
          status: ProductDetailsStatus.success,
          product: product,
          selectedVariant: selectedVariant,
          selectedImageIndex: 0,
          quantity: 1,
          isFavorite: isFavorite,
        ));
      },
    );
  }

  void _onProductVariantSelected(
    ProductVariantSelected event,
    Emitter<ProductDetailsState> emit,
  ) {
    if (state.product == null) return;

    final variant = state.product!.getVariantById(event.variantId);
    if (variant == null) return;

    // Reset quantity to 1 when variant changes
    // Also reset to first image of variant if it has images, otherwise keep current
    final newImageIndex = variant.images.isNotEmpty ? 0 : state.selectedImageIndex;

    emit(state.copyWith(
      selectedVariant: variant,
      quantity: 1,
      selectedImageIndex: newImageIndex,
    ));
  }

  void _onProductQuantityChanged(
    ProductQuantityChanged event,
    Emitter<ProductDetailsState> emit,
  ) {
    if (state.product == null) return;

    // Validate quantity based on availability
    final availability = state.selectedVariant?.availability ?? state.product!.availability;
    final maxQuantity = availability.maxAvailableQuantity;
    final minQuantity = availability.minOrderQuantity;

    final validatedQuantity = event.quantity.clamp(minQuantity, maxQuantity);

    emit(state.copyWith(quantity: validatedQuantity));
  }

  void _onProductImageSelected(
    ProductImageSelected event,
    Emitter<ProductDetailsState> emit,
  ) {
    if (state.product == null) return;

    final totalImages = state.displayImages.length;
    if (event.index >= 0 && event.index < totalImages) {
      emit(state.copyWith(selectedImageIndex: event.index));
    }
  }

  Future<void> _onProductFavoriteToggled(
    ProductFavoriteToggled event,
    Emitter<ProductDetailsState> emit,
  ) async {
    if (state.product == null) return;

    final currentFavoriteStatus = state.isFavorite;

    // Optimistically update UI
    emit(state.copyWith(isFavorite: !currentFavoriteStatus));

    // Perform the actual operation
    final result = currentFavoriteStatus
        ? await removeFromFavoritesUseCase(
            RemoveFromFavoritesParams(productId: state.product!.id),
          )
        : await addToFavoritesUseCase(
            AddToFavoritesParams(productId: state.product!.id),
          );

    // Revert if operation failed
    result.fold(
      (failure) => emit(state.copyWith(isFavorite: currentFavoriteStatus)),
      (success) => {}, // Keep the optimistic update
    );
  }

  Future<void> _onProductDetailsRefreshed(
    ProductDetailsRefreshed event,
    Emitter<ProductDetailsState> emit,
  ) async {
    if (state.product == null) return;

    add(ProductDetailsRequested(productId: state.product!.id));
  }
}

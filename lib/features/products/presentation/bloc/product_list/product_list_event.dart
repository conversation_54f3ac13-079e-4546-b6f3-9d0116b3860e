part of 'product_list_bloc.dart';

abstract class ProductListEvent extends Equatable {
  const ProductListEvent();

  @override
  List<Object?> get props => [];
}

/// Event to request initial product list
class ProductListRequested extends ProductListEvent {
  const ProductListRequested({this.categoryId});

  final String? categoryId;

  @override
  List<Object?> get props => [categoryId];
}

/// Event to load more products (pagination)
class ProductListLoadMore extends ProductListEvent {
  const ProductListLoadMore();
}

/// Event to refresh the product list
class ProductListRefreshed extends ProductListEvent {
  const ProductListRefreshed();
}

/// Event to search products
class ProductListSearched extends ProductListEvent {
  const ProductListSearched({required this.query});

  final String query;

  @override
  List<Object?> get props => [query];
}

/// Event to change product filter
class ProductListFilterChanged extends ProductListEvent {
  const ProductListFilterChanged({required this.filter});

  final ProductFilter filter;

  @override
  List<Object?> get props => [filter];
}

/// Event to change product sorting
class ProductListSortChanged extends ProductListEvent {
  const ProductListSortChanged({
    required this.sortBy,
    this.sortOrder = SortOrder.asc,
  });

  final ProductSortBy sortBy;
  final SortOrder sortOrder;

  @override
  List<Object?> get props => [sortBy, sortOrder];
}

/// Event to change view mode (grid/list)
class ProductListViewModeChanged extends ProductListEvent {
  const ProductListViewModeChanged({required this.viewMode});

  final ProductViewMode viewMode;

  @override
  List<Object?> get props => [viewMode];
}

/// Event to toggle product favorite status
class ProductFavoriteToggled extends ProductListEvent {
  const ProductFavoriteToggled({required this.productId});

  final String productId;

  @override
  List<Object?> get props => [productId];
}

/// Event to change category
class ProductListCategoryChanged extends ProductListEvent {
  const ProductListCategoryChanged({this.categoryId});

  final String? categoryId;

  @override
  List<Object?> get props => [categoryId];
}

/// Product view mode enumeration
enum ProductViewMode {
  grid,
  list,
}

import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/product_image.dart';

part 'product_image_model.g.dart';

@JsonSerializable()
class ProductImageModel {
  const ProductImageModel({
    required this.id,
    required this.url,
    required this.altText,
    this.position = 0,
    this.width,
    this.height,
    this.thumbnailUrl,
    this.mediumUrl,
    this.largeUrl,
    this.isMain = false,
  });

  final String id;
  final String url;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'alt_text')
  final String altText;
  final int position;
  final int? width;
  final int? height;
  @J<PERSON><PERSON>ey(name: 'thumbnail_url')
  final String? thumbnailUrl;
  @J<PERSON><PERSON><PERSON>(name: 'medium_url')
  final String? mediumUrl;
  @Json<PERSON>ey(name: 'large_url')
  final String? largeUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_main')
  final bool isMain;

  factory ProductImageModel.fromJson(Map<String, dynamic> json) => 
      _$ProductImageModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProductImageModelToJson(this);

  /// Convert model to domain entity
  ProductImage toEntity() {
    return ProductImage(
      id: id,
      url: url,
      altText: altText,
      position: position,
      width: width,
      height: height,
      thumbnailUrl: thumbnailUrl,
      mediumUrl: mediumUrl,
      largeUrl: largeUrl,
      isMain: isMain,
    );
  }

  /// Create model from domain entity
  factory ProductImageModel.fromEntity(ProductImage entity) {
    return ProductImageModel(
      id: entity.id,
      url: entity.url,
      altText: entity.altText,
      position: entity.position,
      width: entity.width,
      height: entity.height,
      thumbnailUrl: entity.thumbnailUrl,
      mediumUrl: entity.mediumUrl,
      largeUrl: entity.largeUrl,
      isMain: entity.isMain,
    );
  }
}

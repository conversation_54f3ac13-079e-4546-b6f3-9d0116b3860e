import 'package:equatable/equatable.dart';
import '../../../../shared/domain/entities/base_entity.dart';
import '../value_objects/money.dart';
import '../value_objects/product_category.dart';
import '../value_objects/product_availability.dart';
import 'product_image.dart';
import 'product_variant.dart';
import 'product_review.dart';

/// Core product entity for the ecommerce application
class Product extends BaseEntity {
  const Product({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    required this.availability,
    required this.images,
    this.shortDescription,
    this.sku,
    this.brand,
    this.weight,
    this.dimensions,
    this.variants = const [],
    this.tags = const [],
    this.compareAtPrice,
    this.vendor,
    this.handle,
    this.seoTitle,
    this.seoDescription,
    this.metaKeywords = const [],
    this.isFeatured = false,
    this.isDigital = false,
    this.requiresShipping = true,
    this.taxable = true,
    this.reviews = const [],
    this.averageRating = 0.0,
    this.totalReviews = 0,
    this.relatedProductIds = const [],
    this.customFields = const {},
  });

  final String name;
  final String description;
  final String? shortDescription;
  final String? sku;
  final String? brand;
  final Money price;
  final Money? compareAtPrice;
  final String? vendor;
  final String? handle;
  final ProductCategory category;
  final ProductAvailability availability;
  final List<ProductImage> images;
  final List<ProductVariant> variants;
  final List<String> tags;
  final double? weight;
  final Map<String, double>? dimensions; // e.g., {'length': 10.0, 'width': 5.0, 'height': 2.0}
  final String? seoTitle;
  final String? seoDescription;
  final List<String> metaKeywords;
  final bool isFeatured;
  final bool isDigital;
  final bool requiresShipping;
  final bool taxable;
  final List<ProductReview> reviews;
  final double averageRating;
  final int totalReviews;
  final List<String> relatedProductIds;
  final Map<String, dynamic> customFields;

  /// Get the main product image
  ProductImage? get mainImage => images.isNotEmpty ? images.first : null;

  /// Get all product images including variant images
  List<ProductImage> get allImages {
    final allImages = <ProductImage>[...images];
    for (final variant in variants) {
      allImages.addAll(variant.images);
    }
    return allImages;
  }

  /// Check if product is on sale
  bool get isOnSale => compareAtPrice != null && compareAtPrice! > price;

  /// Get discount percentage if on sale
  double get discountPercentage {
    if (!isOnSale || compareAtPrice == null) return 0.0;
    return ((compareAtPrice!.amount - price.amount) / compareAtPrice!.amount) * 100;
  }

  /// Get savings amount if on sale
  Money? get savingsAmount {
    if (!isOnSale || compareAtPrice == null) return null;
    return compareAtPrice! - price;
  }

  /// Check if product has variants
  bool get hasVariants => variants.isNotEmpty;

  /// Get available variants only
  List<ProductVariant> get availableVariants => 
      variants.where((variant) => variant.availability.isAvailable).toList();

  /// Get price range for variants
  ({Money min, Money max})? get priceRange {
    if (!hasVariants) return null;
    
    final prices = variants.map((v) => v.price).toList();
    if (prices.isEmpty) return null;
    
    prices.sort((a, b) => a.amount.compareTo(b.amount));
    return (min: prices.first, max: prices.last);
  }

  /// Check if product is available for purchase
  bool get isAvailable => availability.isAvailable && (hasVariants ? availableVariants.isNotEmpty : true);

  /// Get display price (lowest variant price if has variants, otherwise base price)
  Money get displayPrice {
    if (!hasVariants) return price;
    
    final variantPrices = variants.map((v) => v.price).toList();
    if (variantPrices.isEmpty) return price;
    
    variantPrices.sort((a, b) => a.amount.compareTo(b.amount));
    return variantPrices.first;
  }

  /// Get formatted price range string
  String get formattedPriceRange {
    if (!hasVariants) return price.format();
    
    final range = priceRange;
    if (range == null) return price.format();
    
    if (range.min.amount == range.max.amount) {
      return range.min.format();
    }
    
    return '${range.min.format()} - ${range.max.format()}';
  }

  /// Check if product has good rating (>= 4.0)
  bool get hasGoodRating => averageRating >= 4.0 && totalReviews > 0;

  /// Get rating stars (0-5)
  int get ratingStars => averageRating.round().clamp(0, 5);

  /// Get variant by ID
  ProductVariant? getVariantById(String variantId) {
    try {
      return variants.firstWhere((variant) => variant.id == variantId);
    } catch (e) {
      return null;
    }
  }

  /// Get variants by option (e.g., color, size)
  List<ProductVariant> getVariantsByOption(String optionName, String optionValue) {
    return variants.where((variant) => 
        variant.options.containsKey(optionName) && 
        variant.options[optionName] == optionValue
    ).toList();
  }

  /// Get all unique option values for a given option name
  List<String> getOptionValues(String optionName) {
    final values = <String>{};
    for (final variant in variants) {
      final value = variant.options[optionName];
      if (value != null) values.add(value);
    }
    return values.toList()..sort();
  }

  /// Get all option names
  List<String> get optionNames {
    final names = <String>{};
    for (final variant in variants) {
      names.addAll(variant.options.keys);
    }
    return names.toList()..sort();
  }

  /// Create a copy with modified values
  Product copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? name,
    String? description,
    String? shortDescription,
    String? sku,
    String? brand,
    Money? price,
    Money? compareAtPrice,
    String? vendor,
    String? handle,
    ProductCategory? category,
    ProductAvailability? availability,
    List<ProductImage>? images,
    List<ProductVariant>? variants,
    List<String>? tags,
    double? weight,
    Map<String, double>? dimensions,
    String? seoTitle,
    String? seoDescription,
    List<String>? metaKeywords,
    bool? isFeatured,
    bool? isDigital,
    bool? requiresShipping,
    bool? taxable,
    List<ProductReview>? reviews,
    double? averageRating,
    int? totalReviews,
    List<String>? relatedProductIds,
    Map<String, dynamic>? customFields,
  }) {
    return Product(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      description: description ?? this.description,
      shortDescription: shortDescription ?? this.shortDescription,
      sku: sku ?? this.sku,
      brand: brand ?? this.brand,
      price: price ?? this.price,
      compareAtPrice: compareAtPrice ?? this.compareAtPrice,
      vendor: vendor ?? this.vendor,
      handle: handle ?? this.handle,
      category: category ?? this.category,
      availability: availability ?? this.availability,
      images: images ?? this.images,
      variants: variants ?? this.variants,
      tags: tags ?? this.tags,
      weight: weight ?? this.weight,
      dimensions: dimensions ?? this.dimensions,
      seoTitle: seoTitle ?? this.seoTitle,
      seoDescription: seoDescription ?? this.seoDescription,
      metaKeywords: metaKeywords ?? this.metaKeywords,
      isFeatured: isFeatured ?? this.isFeatured,
      isDigital: isDigital ?? this.isDigital,
      requiresShipping: requiresShipping ?? this.requiresShipping,
      taxable: taxable ?? this.taxable,
      reviews: reviews ?? this.reviews,
      averageRating: averageRating ?? this.averageRating,
      totalReviews: totalReviews ?? this.totalReviews,
      relatedProductIds: relatedProductIds ?? this.relatedProductIds,
      customFields: customFields ?? this.customFields,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        name,
        description,
        shortDescription,
        sku,
        brand,
        price,
        compareAtPrice,
        vendor,
        handle,
        category,
        availability,
        images,
        variants,
        tags,
        weight,
        dimensions,
        seoTitle,
        seoDescription,
        metaKeywords,
        isFeatured,
        isDigital,
        requiresShipping,
        taxable,
        reviews,
        averageRating,
        totalReviews,
        relatedProductIds,
        customFields,
      ];

  @override
  String toString() => 'Product(id: $id, name: $name, price: $price)';
}

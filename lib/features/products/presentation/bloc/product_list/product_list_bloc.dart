import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import '../../../../../core/errors/failures.dart';
import '../../../domain/entities/product.dart';
import '../../../domain/repositories/product_repository.dart';
import '../../../domain/usecases/get_products_usecase.dart';
import '../../../domain/usecases/search_products_usecase.dart';
import '../../../domain/usecases/manage_favorites_usecase.dart';

part 'product_list_event.dart';
part 'product_list_state.dart';

@injectable
class ProductListBloc extends Bloc<ProductListEvent, ProductListState> {
  ProductListBloc({
    required this.getProductsUseCase,
    required this.searchProductsUseCase,
    required this.addToFavoritesUseCase,
    required this.removeFromFavoritesUseCase,
    required this.isProductFavoriteUseCase,
  }) : super(const ProductListState()) {
    on<ProductListRequested>(_onProductListRequested);
    on<ProductListLoadMore>(_onProductListLoadMore);
    on<ProductListRefreshed>(_onProductListRefreshed);
    on<ProductListSearched>(_onProductListSearched);
    on<ProductListFilterChanged>(_onProductListFilterChanged);
    on<ProductListSortChanged>(_onProductListSortChanged);
    on<ProductListViewModeChanged>(_onProductListViewModeChanged);
    on<ProductFavoriteToggled>(_onProductFavoriteToggled);
    on<ProductListCategoryChanged>(_onProductListCategoryChanged);
  }

  final GetProductsUseCase getProductsUseCase;
  final SearchProductsUseCase searchProductsUseCase;
  final AddToFavoritesUseCase addToFavoritesUseCase;
  final RemoveFromFavoritesUseCase removeFromFavoritesUseCase;
  final IsProductFavoriteUseCase isProductFavoriteUseCase;

  static const int _pageSize = 20;

  Future<void> _onProductListRequested(
    ProductListRequested event,
    Emitter<ProductListState> emit,
  ) async {
    emit(state.copyWith(status: ProductListStatus.loading));

    final result = await getProductsUseCase(GetProductsParams(
      page: 1,
      limit: _pageSize,
      categoryId: event.categoryId,
      sortBy: state.sortBy,
      sortOrder: state.sortOrder,
      filter: state.filter,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductListStatus.failure,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductListStatus.success,
        products: products,
        hasReachedMax: products.length < _pageSize,
        currentPage: 1,
        categoryId: event.categoryId,
      )),
    );
  }

  Future<void> _onProductListLoadMore(
    ProductListLoadMore event,
    Emitter<ProductListState> emit,
  ) async {
    if (state.hasReachedMax || state.status == ProductListStatus.loadingMore) return;

    emit(state.copyWith(status: ProductListStatus.loadingMore));

    final nextPage = state.currentPage + 1;
    final result = await _getProducts(nextPage);

    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductListStatus.success, // Keep previous state
        errorMessage: failure.message,
      )),
      (newProducts) {
        final allProducts = List<Product>.from(state.products)..addAll(newProducts);
        emit(state.copyWith(
          status: ProductListStatus.success,
          products: allProducts,
          hasReachedMax: newProducts.length < _pageSize,
          currentPage: nextPage,
        ));
      },
    );
  }

  Future<void> _onProductListRefreshed(
    ProductListRefreshed event,
    Emitter<ProductListState> emit,
  ) async {
    emit(state.copyWith(status: ProductListStatus.loading));

    final result = await _getProducts(1);

    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductListStatus.failure,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductListStatus.success,
        products: products,
        hasReachedMax: products.length < _pageSize,
        currentPage: 1,
      )),
    );
  }

  Future<void> _onProductListSearched(
    ProductListSearched event,
    Emitter<ProductListState> emit,
  ) async {
    if (event.query.trim().isEmpty) {
      // Clear search and reload products
      emit(state.copyWith(
        searchQuery: '',
        isSearchMode: false,
      ));
      add(const ProductListRequested());
      return;
    }

    emit(state.copyWith(
      status: ProductListStatus.loading,
      searchQuery: event.query,
      isSearchMode: true,
    ));

    final result = await searchProductsUseCase(SearchProductsParams(
      query: event.query,
      page: 1,
      limit: _pageSize,
      categoryId: state.categoryId,
      sortBy: state.sortBy,
      sortOrder: state.sortOrder,
      filter: state.filter,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductListStatus.failure,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductListStatus.success,
        products: products,
        hasReachedMax: products.length < _pageSize,
        currentPage: 1,
      )),
    );
  }

  Future<void> _onProductListFilterChanged(
    ProductListFilterChanged event,
    Emitter<ProductListState> emit,
  ) async {
    emit(state.copyWith(
      filter: event.filter,
      status: ProductListStatus.loading,
    ));

    final result = await _getProducts(1);

    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductListStatus.failure,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductListStatus.success,
        products: products,
        hasReachedMax: products.length < _pageSize,
        currentPage: 1,
      )),
    );
  }

  Future<void> _onProductListSortChanged(
    ProductListSortChanged event,
    Emitter<ProductListState> emit,
  ) async {
    emit(state.copyWith(
      sortBy: event.sortBy,
      sortOrder: event.sortOrder,
      status: ProductListStatus.loading,
    ));

    final result = await _getProducts(1);

    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductListStatus.failure,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductListStatus.success,
        products: products,
        hasReachedMax: products.length < _pageSize,
        currentPage: 1,
      )),
    );
  }

  void _onProductListViewModeChanged(
    ProductListViewModeChanged event,
    Emitter<ProductListState> emit,
  ) {
    emit(state.copyWith(viewMode: event.viewMode));
  }

  Future<void> _onProductFavoriteToggled(
    ProductFavoriteToggled event,
    Emitter<ProductListState> emit,
  ) async {
    // Check current favorite status
    final isFavoriteResult = await isProductFavoriteUseCase(
      IsProductFavoriteParams(productId: event.productId),
    );

    await isFavoriteResult.fold(
      (failure) async {
        // Handle error silently or show a message
      },
      (isFavorite) async {
        if (isFavorite) {
          await removeFromFavoritesUseCase(
            RemoveFromFavoritesParams(productId: event.productId),
          );
        } else {
          await addToFavoritesUseCase(
            AddToFavoritesParams(productId: event.productId),
          );
        }
      },
    );
  }

  void _onProductListCategoryChanged(
    ProductListCategoryChanged event,
    Emitter<ProductListState> emit,
  ) {
    emit(state.copyWith(
      categoryId: event.categoryId,
      status: ProductListStatus.loading,
    ));

    add(ProductListRequested(categoryId: event.categoryId));
  }

  /// Helper method to get products based on current state
  Future<Either<Failure, List<Product>>> _getProducts(int page) async {
    if (state.isSearchMode && state.searchQuery.isNotEmpty) {
      return await searchProductsUseCase(SearchProductsParams(
        query: state.searchQuery,
        page: page,
        limit: _pageSize,
        categoryId: state.categoryId,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        filter: state.filter,
      ));
    } else {
      return await getProductsUseCase(GetProductsParams(
        page: page,
        limit: _pageSize,
        categoryId: state.categoryId,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        filter: state.filter,
      ));
    }
  }
}

import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import 'exceptions.dart';
import 'failures.dart';

/// Interface for error handling
abstract class <PERSON>rrorHandler {
  /// Converts exceptions to failures
  Failure handleException(Exception exception);

  /// Logs errors with appropriate level
  void logError(dynamic error, StackTrace? stackTrace);

  /// Handles Dio errors specifically
  Failure handleDioError(DioException error);
}

/// Implementation of error handling
class ErrorHandlerImpl implements <PERSON>rrorHandler {
  ErrorHandlerImpl({
    required Logger logger,
  }) : _logger = logger;

  final Logger _logger;

  @override
  Failure handleException(Exception exception) {
    _logger.e('Exception occurred: $exception');

    switch (exception.runtimeType) {
      case const (NetworkException):
        final networkException = exception as NetworkException;
        return NetworkFailure(
          message: networkException.message,
          code: networkException.code,
          stackTrace: networkException.stackTrace,
        );

      case const (ServerException):
        final serverException = exception as ServerException;
        return ServerFailure(
          message: serverException.message,
          code: serverException.code,
          stackTrace: serverException.stackTrace,
        );

      case const (CacheException):
        final cacheException = exception as CacheException;
        return CacheFailure(
          message: cacheException.message,
          code: cacheException.code,
          stackTrace: cacheException.stackTrace,
        );

      case const (AuthenticationException):
        final authException = exception as AuthenticationException;
        return AuthenticationFailure(
          message: authException.message,
          code: authException.code,
          stackTrace: authException.stackTrace,
        );

      case const (ValidationException):
        final validationException = exception as ValidationException;
        return ValidationFailure(
          message: validationException.message,
          code: validationException.code,
          stackTrace: validationException.stackTrace,
        );

      case const (PlatformException):
        final platformException = exception as PlatformException;
        return PlatformFailure(
          message: platformException.message,
          code: platformException.code,
          stackTrace: platformException.stackTrace,
        );

      default:
        return UnknownFailure(
          message: exception.toString(),
          stackTrace: StackTrace.current,
        );
    }
  }

  @override
  void logError(dynamic error, StackTrace? stackTrace) {
    _logger.e('Error occurred: $error', error: error, stackTrace: stackTrace);
  }

  @override
  Failure handleDioError(DioException error) {
    _logger.e('Dio error occurred: ${error.message}', error: error);

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkFailure(
          message: 'Connection timeout. Please check your internet connection.',
          code: 'TIMEOUT',
        );

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = _getErrorMessageFromStatusCode(statusCode);
        return ServerFailure(
          message: message,
          code: statusCode?.toString(),
        );

      case DioExceptionType.cancel:
        return const NetworkFailure(
          message: 'Request was cancelled',
          code: 'CANCELLED',
        );

      case DioExceptionType.connectionError:
        return const NetworkFailure(
          message: 'No internet connection. Please check your network settings.',
          code: 'NO_CONNECTION',
        );

      case DioExceptionType.badCertificate:
        return const NetworkFailure(
          message: 'Certificate verification failed',
          code: 'BAD_CERTIFICATE',
        );

      case DioExceptionType.unknown:
        return NetworkFailure(
          message: error.message ?? 'An unknown network error occurred',
          code: 'UNKNOWN',
        );
    }
  }

  String _getErrorMessageFromStatusCode(int? statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Unauthorized. Please log in again.';
      case 403:
        return 'Access forbidden. You do not have permission.';
      case 404:
        return 'Resource not found.';
      case 422:
        return 'Validation error. Please check your input.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Internal server error. Please try again later.';
      case 502:
        return 'Bad gateway. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      default:
        return 'Server error occurred. Please try again later.';
    }
  }
}

import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/product_review.dart';
import '../../domain/repositories/product_repository.dart';
import '../../domain/value_objects/product_category.dart';
import '../datasources/product_local_datasource.dart';
import '../datasources/product_remote_datasource.dart';

@LazySingleton(as: ProductRepository)
class ProductRepositoryImpl implements ProductRepository {
  const ProductRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  final ProductRemoteDataSource remoteDataSource;
  final ProductLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  @override
  Future<Either<Failure, List<Product>>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? query,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    try {
      // Check if we have network connectivity
      if (await networkInfo.isConnected) {
        // Try to fetch from remote
        final remoteProducts = await remoteDataSource.getProducts(
          page: page,
          limit: limit,
          categoryId: categoryId,
          query: query,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );

        // Cache the results
        final cacheKey = _buildProductsCacheKey(
          page: page,
          limit: limit,
          categoryId: categoryId,
          query: query,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );
        await localDataSource.cacheProducts(remoteProducts, cacheKey: cacheKey);

        // Convert to entities
        final products = remoteProducts.map((model) => model.toEntity()).toList();
        return Right(products);
      } else {
        // No network, try to get from cache
        final cacheKey = _buildProductsCacheKey(
          page: page,
          limit: limit,
          categoryId: categoryId,
          query: query,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );
        final cachedProducts = await localDataSource.getCachedProducts(cacheKey: cacheKey);

        if (cachedProducts != null) {
          final products = cachedProducts.map((model) => model.toEntity()).toList();
          return Right(products);
        } else {
          return const Left(NetworkFailure(message: 'No internet connection and no cached data available'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Product>> getProduct(String productId) async {
    try {
      // First check cache for quick access
      final cachedProduct = await localDataSource.getCachedProduct(productId);
      
      if (await networkInfo.isConnected) {
        // Try to fetch fresh data from remote
        try {
          final remoteProduct = await remoteDataSource.getProduct(productId);
          
          // Cache the fresh data
          await localDataSource.cacheProduct(remoteProduct);
          
          return Right(remoteProduct.toEntity());
        } catch (e) {
          // If remote fails but we have cache, use cache
          if (cachedProduct != null) {
            return Right(cachedProduct.toEntity());
          }
          rethrow;
        }
      } else {
        // No network, use cache if available
        if (cachedProduct != null) {
          return Right(cachedProduct.toEntity());
        } else {
          return const Left(NetworkFailure(message: 'No internet connection and product not cached'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> searchProducts({
    required String query,
    int page = 1,
    int limit = 20,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteProducts = await remoteDataSource.searchProducts(
          query: query,
          page: page,
          limit: limit,
          categoryId: categoryId,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );

        // Cache search results
        await localDataSource.cacheSearchResults(
          query: query,
          products: remoteProducts,
          categoryId: categoryId,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );

        final products = remoteProducts.map((model) => model.toEntity()).toList();
        return Right(products);
      } else {
        // Try to get cached search results
        final cachedProducts = await localDataSource.getCachedSearchResults(
          query: query,
          categoryId: categoryId,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );

        if (cachedProducts != null) {
          final products = cachedProducts.map((model) => model.toEntity()).toList();
          return Right(products);
        } else {
          return const Left(NetworkFailure(message: 'No internet connection and no cached search results'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getProductsByCategory({
    required String categoryId,
    int page = 1,
    int limit = 20,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteProducts = await remoteDataSource.getProductsByCategory(
          categoryId: categoryId,
          page: page,
          limit: limit,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );

        // Cache category products
        await localDataSource.cacheCategoryProducts(
          categoryId: categoryId,
          products: remoteProducts,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );

        final products = remoteProducts.map((model) => model.toEntity()).toList();
        return Right(products);
      } else {
        // Try to get cached category products
        final cachedProducts = await localDataSource.getCachedCategoryProducts(
          categoryId: categoryId,
          sortBy: sortBy,
          sortOrder: sortOrder,
          filter: filter,
        );

        if (cachedProducts != null) {
          final products = cachedProducts.map((model) => model.toEntity()).toList();
          return Right(products);
        } else {
          return const Left(NetworkFailure(message: 'No internet connection and no cached category products'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getFeaturedProducts({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteProducts = await remoteDataSource.getFeaturedProducts(
          page: page,
          limit: limit,
        );

        // Cache featured products
        await localDataSource.cacheFeaturedProducts(remoteProducts);

        final products = remoteProducts.map((model) => model.toEntity()).toList();
        return Right(products);
      } else {
        // Try to get cached featured products
        final cachedProducts = await localDataSource.getCachedFeaturedProducts();

        if (cachedProducts != null) {
          final products = cachedProducts.map((model) => model.toEntity()).toList();
          return Right(products);
        } else {
          return const Left(NetworkFailure(message: 'No internet connection and no cached featured products'));
        }
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getRelatedProducts({
    required String productId,
    int limit = 10,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteProducts = await remoteDataSource.getRelatedProducts(
          productId: productId,
          limit: limit,
        );

        final products = remoteProducts.map((model) => model.toEntity()).toList();
        return Right(products);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection for related products'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getRecommendedProducts({
    String? userId,
    String? productId,
    int limit = 10,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteProducts = await remoteDataSource.getRecommendedProducts(
          userId: userId,
          productId: productId,
          limit: limit,
        );

        final products = remoteProducts.map((model) => model.toEntity()).toList();
        return Right(products);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection for recommendations'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getProductsByIds(List<String> productIds) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteProducts = await remoteDataSource.getProductsByIds(productIds);
        final products = remoteProducts.map((model) => model.toEntity()).toList();
        return Right(products);
      } else {
        // Try to get from cache
        final cachedProducts = <Product>[];
        for (final productId in productIds) {
          final cachedProduct = await localDataSource.getCachedProduct(productId);
          if (cachedProduct != null) {
            cachedProducts.add(cachedProduct.toEntity());
          }
        }
        return Right(cachedProducts);
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  // Placeholder implementations for methods that would need additional data sources
  @override
  Future<Either<Failure, List<ProductCategory>>> getCategories({
    int page = 1,
    int limit = 50,
    String? parentId,
  }) async {
    // This would need a separate category data source
    return const Left(UnknownFailure(message: 'Categories not implemented yet'));
  }

  @override
  Future<Either<Failure, ProductCategory>> getCategory(String categoryId) async {
    // This would need a separate category data source
    return const Left(UnknownFailure(message: 'Category details not implemented yet'));
  }

  @override
  Future<Either<Failure, List<ProductReview>>> getProductReviews({
    required String productId,
    int page = 1,
    int limit = 20,
    ReviewSortBy? sortBy,
    SortOrder sortOrder = SortOrder.desc,
  }) async {
    // This would need a separate reviews data source
    return const Left(UnknownFailure(message: 'Product reviews not implemented yet'));
  }

  @override
  Future<Either<Failure, void>> addToFavorites(String productId) async {
    try {
      await localDataSource.addToFavorites(productId);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> removeFromFavorites(String productId) async {
    try {
      await localDataSource.removeFromFavorites(productId);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getFavoriteProducts({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final favoriteIds = await localDataSource.getFavoriteProductIds();
      
      if (favoriteIds.isEmpty) {
        return const Right([]);
      }

      // Get paginated subset
      final startIndex = (page - 1) * limit;
      final endIndex = (startIndex + limit).clamp(0, favoriteIds.length);
      final paginatedIds = favoriteIds.sublist(startIndex, endIndex);

      // Get products by IDs
      return await getProductsByIds(paginatedIds);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isProductFavorite(String productId) async {
    try {
      final isFavorite = await localDataSource.isProductFavorite(productId);
      return Right(isFavorite);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getRecentlyViewedProducts({
    int limit = 10,
  }) async {
    try {
      final recentlyViewedIds = await localDataSource.getRecentlyViewedProductIds(limit: limit);
      
      if (recentlyViewedIds.isEmpty) {
        return const Right([]);
      }

      return await getProductsByIds(recentlyViewedIds);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> addToRecentlyViewed(String productId) async {
    try {
      await localDataSource.addToRecentlyViewed(productId);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> clearRecentlyViewed() async {
    try {
      await localDataSource.clearRecentlyViewed();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  /// Build cache key for products
  String _buildProductsCacheKey({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? query,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) {
    final keyParts = [
      'products',
      'page:$page',
      'limit:$limit',
      'category:${categoryId ?? 'all'}',
      'query:${query ?? 'none'}',
      'sort:${sortBy?.name ?? 'default'}',
      'order:${sortOrder.name}',
    ];
    
    if (filter != null && !filter.isEmpty) {
      keyParts.add('filter:${filter.hashCode}');
    }
    
    return keyParts.join('|');
  }
}

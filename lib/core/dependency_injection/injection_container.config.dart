// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:ecommerce_app/core/network/network_info.dart' as _i843;
import 'package:ecommerce_app/core/platform/platform_config.dart' as _i628;
import 'package:ecommerce_app/core/storage/cache_manager.dart' as _i400;
import 'package:ecommerce_app/features/platform_integration/data/datasources/mock_remote_datasource.dart'
    as _i109;
import 'package:ecommerce_app/features/platform_integration/data/datasources/platform_remote_datasource.dart'
    as _i620;
import 'package:ecommerce_app/features/platform_integration/data/repositories/platform_repository_impl.dart'
    as _i967;
import 'package:ecommerce_app/features/platform_integration/domain/repositories/platform_repository.dart'
    as _i1042;
import 'package:ecommerce_app/features/products/data/datasources/hive_product_local_datasource.dart'
    as _i980;
import 'package:ecommerce_app/features/products/data/datasources/platform_product_remote_datasource.dart'
    as _i914;
import 'package:ecommerce_app/features/products/data/datasources/product_local_datasource.dart'
    as _i328;
import 'package:ecommerce_app/features/products/data/datasources/product_remote_datasource.dart'
    as _i696;
import 'package:ecommerce_app/features/products/data/repositories/product_repository_impl.dart'
    as _i83;
import 'package:ecommerce_app/features/products/domain/repositories/product_repository.dart'
    as _i946;
import 'package:ecommerce_app/features/products/domain/usecases/get_product_categories_usecase.dart'
    as _i135;
import 'package:ecommerce_app/features/products/domain/usecases/get_product_details_usecase.dart'
    as _i454;
import 'package:ecommerce_app/features/products/domain/usecases/get_products_usecase.dart'
    as _i826;
import 'package:ecommerce_app/features/products/domain/usecases/manage_favorites_usecase.dart'
    as _i540;
import 'package:ecommerce_app/features/products/domain/usecases/search_products_usecase.dart'
    as _i727;
import 'package:ecommerce_app/features/products/presentation/bloc/product_details/product_details_bloc.dart'
    as _i412;
import 'package:ecommerce_app/features/products/presentation/bloc/product_list/product_list_bloc.dart'
    as _i753;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.factory<_i628.PlatformConfig>(
        () => _i628.PlatformConfig.defaultConfig());
    gh.lazySingleton<_i620.PlatformRemoteDataSource>(
        () => _i109.MockRemoteDataSource(gh<_i628.PlatformConfig>()));
    gh.lazySingleton<_i843.NetworkInfo>(() => _i843.NetworkInfoImpl());
    gh.lazySingleton<_i400.CacheManager>(() => _i400.HiveCacheManager());
    gh.lazySingleton<_i328.ProductLocalDataSource>(
        () => _i980.HiveProductLocalDataSource(gh<_i400.CacheManager>()));
    gh.lazySingleton<_i1042.PlatformRepository>(
        () => _i967.PlatformRepositoryImpl(
              remoteDataSource: gh<_i620.PlatformRemoteDataSource>(),
              config: gh<_i628.PlatformConfig>(),
            ));
    gh.lazySingleton<_i696.ProductRemoteDataSource>(() =>
        _i914.PlatformProductRemoteDataSource(gh<_i1042.PlatformRepository>()));
    gh.lazySingleton<_i946.ProductRepository>(() => _i83.ProductRepositoryImpl(
          remoteDataSource: gh<_i696.ProductRemoteDataSource>(),
          localDataSource: gh<_i328.ProductLocalDataSource>(),
          networkInfo: gh<_i843.NetworkInfo>(),
        ));
    gh.factory<_i727.SearchProductsUseCase>(
        () => _i727.SearchProductsUseCase(gh<_i946.ProductRepository>()));
    gh.factory<_i135.GetProductCategoriesUseCase>(
        () => _i135.GetProductCategoriesUseCase(gh<_i946.ProductRepository>()));
    gh.factory<_i826.GetProductsUseCase>(
        () => _i826.GetProductsUseCase(gh<_i946.ProductRepository>()));
    gh.factory<_i540.AddToFavoritesUseCase>(
        () => _i540.AddToFavoritesUseCase(gh<_i946.ProductRepository>()));
    gh.factory<_i540.RemoveFromFavoritesUseCase>(
        () => _i540.RemoveFromFavoritesUseCase(gh<_i946.ProductRepository>()));
    gh.factory<_i540.GetFavoriteProductsUseCase>(
        () => _i540.GetFavoriteProductsUseCase(gh<_i946.ProductRepository>()));
    gh.factory<_i540.IsProductFavoriteUseCase>(
        () => _i540.IsProductFavoriteUseCase(gh<_i946.ProductRepository>()));
    gh.factory<_i454.GetProductDetailsUseCase>(
        () => _i454.GetProductDetailsUseCase(gh<_i946.ProductRepository>()));
    gh.factory<_i753.ProductListBloc>(() => _i753.ProductListBloc(
          getProductsUseCase: gh<_i826.GetProductsUseCase>(),
          searchProductsUseCase: gh<_i727.SearchProductsUseCase>(),
          addToFavoritesUseCase: gh<_i540.AddToFavoritesUseCase>(),
          removeFromFavoritesUseCase: gh<_i540.RemoveFromFavoritesUseCase>(),
          isProductFavoriteUseCase: gh<_i540.IsProductFavoriteUseCase>(),
        ));
    gh.factory<_i412.ProductDetailsBloc>(() => _i412.ProductDetailsBloc(
          getProductDetailsUseCase: gh<_i454.GetProductDetailsUseCase>(),
          addToFavoritesUseCase: gh<_i540.AddToFavoritesUseCase>(),
          removeFromFavoritesUseCase: gh<_i540.RemoveFromFavoritesUseCase>(),
          isProductFavoriteUseCase: gh<_i540.IsProductFavoriteUseCase>(),
        ));
    return this;
  }
}

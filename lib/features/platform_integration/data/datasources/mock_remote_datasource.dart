import 'package:injectable/injectable.dart';
import '../../../../core/platform/platform_config.dart';
import '../models/platform_product_model.dart';
import '../models/platform_collection_model.dart';
import 'platform_remote_datasource.dart';

/// Mock implementation of platform remote data source for development and testing
@LazySingleton(as: PlatformRemoteDataSource)
class MockRemoteDataSource implements PlatformRemoteDataSource {
  MockRemoteDataSource(this.config);

  final PlatformConfig config;

  @override
  Future<List<PlatformProductModel>> getProducts({
    int page = 1,
    int limit = 20,
    String? collectionId,
    String? query,
    Map<String, dynamic>? filters,
  }) async {
    // Simulate network delay
    await Future<void>.delayed(const Duration(milliseconds: 500));

    final allProducts = _getMockProducts();

    // Apply filtering
    var filteredProducts = allProducts;

    if (collectionId != null) {
      // Filter by collection (simplified)
      filteredProducts = allProducts.where((p) =>
          p.productType?.toLowerCase().contains(collectionId.toLowerCase()) ?? false
      ).toList();
    }

    if (query != null && query.isNotEmpty) {
      filteredProducts = filteredProducts.where((p) =>
          p.title.toLowerCase().contains(query.toLowerCase()) ||
          p.description.toLowerCase().contains(query.toLowerCase())
      ).toList();
    }

    // Apply pagination
    final startIndex = (page - 1) * limit;
    final endIndex = startIndex + limit;

    if (startIndex >= filteredProducts.length) {
      return [];
    }

    return filteredProducts.sublist(
      startIndex,
      endIndex > filteredProducts.length ? filteredProducts.length : endIndex,
    );
  }

  @override
  Future<PlatformProductModel> getProduct(String productId) async {
    await Future<void>.delayed(const Duration(milliseconds: 300));

    final products = _getMockProducts();
    final product = products.firstWhere(
      (p) => p.id == productId,
      orElse: () => throw Exception('Product not found'),
    );

    return product;
  }

  @override
  Future<List<PlatformCollectionModel>> getCollections({
    int page = 1,
    int limit = 20,
  }) async {
    await Future<void>.delayed(const Duration(milliseconds: 400));

    final allCollections = _getMockCollections();

    // Apply pagination
    final startIndex = (page - 1) * limit;
    final endIndex = startIndex + limit;

    if (startIndex >= allCollections.length) {
      return [];
    }

    return allCollections.sublist(
      startIndex,
      endIndex > allCollections.length ? allCollections.length : endIndex,
    );
  }

  @override
  Future<PlatformCollectionModel> getCollection(String collectionId) async {
    await Future<void>.delayed(const Duration(milliseconds: 300));

    final collections = _getMockCollections();
    final collection = collections.firstWhere(
      (c) => c.id == collectionId,
      orElse: () => throw Exception('Collection not found'),
    );

    return collection;
  }

  @override
  Future<List<PlatformProductModel>> searchProducts({
    required String query,
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  }) async {
    return getProducts(
      page: page,
      limit: limit,
      query: query,
      filters: filters,
    );
  }

  @override
  Future<List<PlatformProductModel>> getRecommendedProducts({
    String? productId,
    int limit = 10,
  }) async {
    await Future<void>.delayed(const Duration(milliseconds: 400));

    final products = _getMockProducts();
    return products.take(limit).toList();
  }

  @override
  Future<bool> checkProductAvailability(
    String productId, {
    String? variantId,
    int quantity = 1,
  }) async {
    await Future<void>.delayed(const Duration(milliseconds: 200));

    try {
      final product = await getProduct(productId);

      if (variantId != null) {
        final variant = product.variants.firstWhere(
          (v) => v.id == variantId,
          orElse: () => throw Exception('Variant not found'),
        );
        return variant.isAvailable && variant.inventoryQuantity >= quantity;
      }

      return product.isAvailable;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>> getPlatformConfig() async {
    await Future<void>.delayed(const Duration(milliseconds: 300));

    return {
      'name': 'Mock Store',
      'domain': 'mock-store.example.com',
      'currency': 'USD',
      'timezone': 'UTC',
      'country': 'US',
      'features': {
        'inventory_tracking': true,
        'multi_currency': false,
        'gift_cards': true,
      },
    };
  }

  @override
  Future<bool> testConnection() async {
    await Future<void>.delayed(const Duration(milliseconds: 100));
    return true; // Mock always returns successful connection
  }

  /// Generate mock products for testing
  List<PlatformProductModel> _getMockProducts() {
    final now = DateTime.now();

    return [
      PlatformProductModel(
        id: '1',
        title: 'Premium Wireless Headphones',
        description: 'High-quality wireless headphones with noise cancellation',
        price: 299.99,
        currency: 'USD',
        images: [
          const PlatformProductImageModel(
            id: '1',
            url: 'https://example.com/headphones.jpg',
            altText: 'Premium Wireless Headphones',
            width: 800,
            height: 600,
            position: 0,
          ),
        ],
        variants: [
          const PlatformProductVariantModel(
            id: '1',
            title: 'Black',
            price: 299.99,
            isAvailable: true,
            inventoryQuantity: 50,
            sku: 'WH-001-BLK',
            options: {'Color': 'Black'},
          ),
          const PlatformProductVariantModel(
            id: '2',
            title: 'White',
            price: 299.99,
            isAvailable: true,
            inventoryQuantity: 30,
            sku: 'WH-001-WHT',
            options: {'Color': 'White'},
          ),
        ],
        isAvailable: true,
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 1)),
        compareAtPrice: 349.99,
        vendor: 'AudioTech',
        productType: 'Electronics',
        tags: ['wireless', 'headphones', 'premium'],
        handle: 'premium-wireless-headphones',
      ),
      PlatformProductModel(
        id: '2',
        title: 'Organic Cotton T-Shirt',
        description: 'Comfortable organic cotton t-shirt in various colors',
        price: 29.99,
        currency: 'USD',
        images: [
          const PlatformProductImageModel(
            id: '2',
            url: 'https://example.com/tshirt.jpg',
            altText: 'Organic Cotton T-Shirt',
            width: 800,
            height: 600,
            position: 0,
          ),
        ],
        variants: [
          const PlatformProductVariantModel(
            id: '3',
            title: 'Small / Blue',
            price: 29.99,
            isAvailable: true,
            inventoryQuantity: 25,
            sku: 'TS-001-S-BLU',
            options: {'Size': 'Small', 'Color': 'Blue'},
          ),
          const PlatformProductVariantModel(
            id: '4',
            title: 'Medium / Blue',
            price: 29.99,
            isAvailable: true,
            inventoryQuantity: 40,
            sku: 'TS-001-M-BLU',
            options: {'Size': 'Medium', 'Color': 'Blue'},
          ),
        ],
        isAvailable: true,
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(days: 2)),
        vendor: 'EcoWear',
        productType: 'Clothing',
        tags: ['organic', 'cotton', 'sustainable'],
        handle: 'organic-cotton-tshirt',
      ),
    ];
  }

  /// Generate mock collections for testing
  List<PlatformCollectionModel> _getMockCollections() {
    final now = DateTime.now();

    return [
      PlatformCollectionModel(
        id: '1',
        title: 'Electronics',
        description: 'Latest electronic gadgets and accessories',
        handle: 'electronics',
        createdAt: now.subtract(const Duration(days: 60)),
        updatedAt: now.subtract(const Duration(days: 5)),
        image: const PlatformCollectionImageModel(
          url: 'https://example.com/electronics-collection.jpg',
          altText: 'Electronics Collection',
          width: 1200,
          height: 600,
        ),
        productCount: 25,
        sortOrder: 'best-selling',
      ),
      PlatformCollectionModel(
        id: '2',
        title: 'Clothing',
        description: 'Sustainable and comfortable clothing for everyone',
        handle: 'clothing',
        createdAt: now.subtract(const Duration(days: 45)),
        updatedAt: now.subtract(const Duration(days: 3)),
        image: const PlatformCollectionImageModel(
          url: 'https://example.com/clothing-collection.jpg',
          altText: 'Clothing Collection',
          width: 1200,
          height: 600,
        ),
        productCount: 40,
        sortOrder: 'created',
      ),
    ];
  }
}

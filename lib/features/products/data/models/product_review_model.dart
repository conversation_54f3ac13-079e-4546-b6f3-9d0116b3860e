import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/product_review.dart';

part 'product_review_model.g.dart';

@JsonSerializable()
class ProductReviewModel {
  const ProductReviewModel({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.productId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.title,
    required this.content,
    this.userEmail,
    this.userAvatar,
    this.isVerifiedPurchase = false,
    this.isApproved = true,
    this.helpfulCount = 0,
    this.notHelpfulCount = 0,
    this.images = const [],
    this.variantId,
    this.variantTitle,
  });

  final String id;
  @Json<PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;
  @Json<PERSON>ey(name: 'product_id')
  final String productId;
  @JsonKey(name: 'user_id')
  final String userId;
  @Json<PERSON>ey(name: 'user_name')
  final String userName;
  @Json<PERSON>ey(name: 'user_email')
  final String? userEmail;
  @Json<PERSON>ey(name: 'user_avatar')
  final String? userAvatar;
  final int rating;
  final String title;
  final String content;
  @JsonKey(name: 'is_verified_purchase')
  final bool isVerifiedPurchase;
  @JsonKey(name: 'is_approved')
  final bool isApproved;
  @JsonKey(name: 'helpful_count')
  final int helpfulCount;
  @JsonKey(name: 'not_helpful_count')
  final int notHelpfulCount;
  final List<String> images;
  @JsonKey(name: 'variant_id')
  final String? variantId;
  @JsonKey(name: 'variant_title')
  final String? variantTitle;

  factory ProductReviewModel.fromJson(Map<String, dynamic> json) => 
      _$ProductReviewModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProductReviewModelToJson(this);

  /// Convert model to domain entity
  ProductReview toEntity() {
    return ProductReview(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt,
      productId: productId,
      userId: userId,
      userName: userName,
      userEmail: userEmail,
      userAvatar: userAvatar,
      rating: rating,
      title: title,
      content: content,
      isVerifiedPurchase: isVerifiedPurchase,
      isApproved: isApproved,
      helpfulCount: helpfulCount,
      notHelpfulCount: notHelpfulCount,
      images: images,
      variantId: variantId,
      variantTitle: variantTitle,
    );
  }

  /// Create model from domain entity
  factory ProductReviewModel.fromEntity(ProductReview entity) {
    return ProductReviewModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      productId: entity.productId,
      userId: entity.userId,
      userName: entity.userName,
      userEmail: entity.userEmail,
      userAvatar: entity.userAvatar,
      rating: entity.rating,
      title: entity.title,
      content: entity.content,
      isVerifiedPurchase: entity.isVerifiedPurchase,
      isApproved: entity.isApproved,
      helpfulCount: entity.helpfulCount,
      notHelpfulCount: entity.notHelpfulCount,
      images: entity.images,
      variantId: entity.variantId,
      variantTitle: entity.variantTitle,
    );
  }
}

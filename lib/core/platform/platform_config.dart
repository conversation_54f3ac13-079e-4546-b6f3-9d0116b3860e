import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'platform_type.dart';

/// Configuration class for platform-specific settings
@injectable
class PlatformConfig extends Equatable {
  const PlatformConfig({
    required this.platformType,
    required this.baseUrl,
    required this.apiKey,
    this.secretKey,
    this.storeId,
    this.version,
    this.timeout = const Duration(seconds: 30),
    this.retryAttempts = 3,
    this.enableLogging = false,
    this.enableCaching = true,
    this.cacheTimeout = const Duration(minutes: 5),
  });

  /// Default factory method for dependency injection
  @factoryMethod
  factory PlatformConfig.defaultConfig() => PlatformConfig.mock();

  final PlatformType platformType;
  final String baseUrl;
  final String apiKey;
  final String? secretKey;
  final String? storeId;
  final String? version;
  final Duration timeout;
  final int retryAttempts;
  final bool enableLogging;
  final bool enableCaching;
  final Duration cacheTimeout;

  /// Create a Shopify configuration
  factory PlatformConfig.shopify({
    required String shopDomain,
    required String accessToken,
    String? version,
  }) {
    return PlatformConfig(
      platformType: PlatformType.shopify,
      baseUrl: 'https://$shopDomain.myshopify.com',
      apiKey: accessToken,
      version: version ?? '2024-01',
      enableLogging: true,
    );
  }

  /// Create a WooCommerce configuration
  factory PlatformConfig.woocommerce({
    required String baseUrl,
    required String consumerKey,
    required String consumerSecret,
  }) {
    return PlatformConfig(
      platformType: PlatformType.woocommerce,
      baseUrl: baseUrl,
      apiKey: consumerKey,
      secretKey: consumerSecret,
      enableLogging: true,
    );
  }

  /// Create a mock configuration for development
  factory PlatformConfig.mock() {
    return const PlatformConfig(
      platformType: PlatformType.mock,
      baseUrl: 'https://mock-api.example.com',
      apiKey: 'mock-api-key',
      enableLogging: true,
      enableCaching: false,
    );
  }

  /// Copy configuration with updated values
  PlatformConfig copyWith({
    PlatformType? platformType,
    String? baseUrl,
    String? apiKey,
    String? secretKey,
    String? storeId,
    String? version,
    Duration? timeout,
    int? retryAttempts,
    bool? enableLogging,
    bool? enableCaching,
    Duration? cacheTimeout,
  }) {
    return PlatformConfig(
      platformType: platformType ?? this.platformType,
      baseUrl: baseUrl ?? this.baseUrl,
      apiKey: apiKey ?? this.apiKey,
      secretKey: secretKey ?? this.secretKey,
      storeId: storeId ?? this.storeId,
      version: version ?? this.version,
      timeout: timeout ?? this.timeout,
      retryAttempts: retryAttempts ?? this.retryAttempts,
      enableLogging: enableLogging ?? this.enableLogging,
      enableCaching: enableCaching ?? this.enableCaching,
      cacheTimeout: cacheTimeout ?? this.cacheTimeout,
    );
  }

  /// Validate configuration
  bool get isValid {
    return baseUrl.isNotEmpty &&
           apiKey.isNotEmpty &&
           timeout.inSeconds > 0 &&
           retryAttempts >= 0;
  }

  @override
  List<Object?> get props => [
        platformType,
        baseUrl,
        apiKey,
        secretKey,
        storeId,
        version,
        timeout,
        retryAttempts,
        enableLogging,
        enableCaching,
        cacheTimeout,
      ];
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) => ProductModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      categoryId: json['category_id'] as String,
      categoryName: json['category_name'] as String,
      categorySlug: json['category_slug'] as String,
      availabilityStatus: json['availability_status'] as String,
      availabilityQuantity: (json['availability_quantity'] as num).toInt(),
      images: (json['images'] as List<dynamic>)
          .map((e) => ProductImageModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      shortDescription: json['short_description'] as String?,
      sku: json['sku'] as String?,
      brand: json['brand'] as String?,
      weight: (json['weight'] as num?)?.toDouble(),
      dimensions: (json['dimensions'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      variants: (json['variants'] as List<dynamic>?)
              ?.map((e) =>
                  ProductVariantModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      compareAtPrice: (json['compare_at_price'] as num?)?.toDouble(),
      vendor: json['vendor'] as String?,
      handle: json['handle'] as String?,
      seoTitle: json['seo_title'] as String?,
      seoDescription: json['seo_description'] as String?,
      metaKeywords: (json['meta_keywords'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isFeatured: json['is_featured'] as bool? ?? false,
      isDigital: json['is_digital'] as bool? ?? false,
      requiresShipping: json['requires_shipping'] as bool? ?? true,
      taxable: json['taxable'] as bool? ?? true,
      reviews: (json['reviews'] as List<dynamic>?)
              ?.map(
                  (e) => ProductReviewModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      averageRating: (json['average_rating'] as num?)?.toDouble() ?? 0.0,
      totalReviews: (json['total_reviews'] as num?)?.toInt() ?? 0,
      relatedProductIds: (json['related_product_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customFields: json['custom_fields'] as Map<String, dynamic>? ?? const {},
      lowStockThreshold: (json['low_stock_threshold'] as num?)?.toInt() ?? 5,
      allowBackorder: json['allow_backorder'] as bool? ?? false,
      estimatedRestockDate: json['estimated_restock_date'] == null
          ? null
          : DateTime.parse(json['estimated_restock_date'] as String),
      maxOrderQuantity: (json['max_order_quantity'] as num?)?.toInt(),
      minOrderQuantity: (json['min_order_quantity'] as num?)?.toInt() ?? 1,
      categoryDescription: json['category_description'] as String?,
      categoryParentId: json['category_parent_id'] as String?,
      categoryImageUrl: json['category_image_url'] as String?,
      categorySortOrder: (json['category_sort_order'] as num?)?.toInt() ?? 0,
      categoryIsActive: json['category_is_active'] as bool? ?? true,
    );

Map<String, dynamic> _$ProductModelToJson(ProductModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'name': instance.name,
      'description': instance.description,
      'short_description': instance.shortDescription,
      'sku': instance.sku,
      'brand': instance.brand,
      'price': instance.price,
      'currency': instance.currency,
      'compare_at_price': instance.compareAtPrice,
      'vendor': instance.vendor,
      'handle': instance.handle,
      'category_id': instance.categoryId,
      'category_name': instance.categoryName,
      'category_slug': instance.categorySlug,
      'category_description': instance.categoryDescription,
      'category_parent_id': instance.categoryParentId,
      'category_image_url': instance.categoryImageUrl,
      'category_sort_order': instance.categorySortOrder,
      'category_is_active': instance.categoryIsActive,
      'availability_status': instance.availabilityStatus,
      'availability_quantity': instance.availabilityQuantity,
      'low_stock_threshold': instance.lowStockThreshold,
      'allow_backorder': instance.allowBackorder,
      'estimated_restock_date':
          instance.estimatedRestockDate?.toIso8601String(),
      'max_order_quantity': instance.maxOrderQuantity,
      'min_order_quantity': instance.minOrderQuantity,
      'images': instance.images.map((e) => e.toJson()).toList(),
      'variants': instance.variants.map((e) => e.toJson()).toList(),
      'tags': instance.tags,
      'weight': instance.weight,
      'dimensions': instance.dimensions,
      'seo_title': instance.seoTitle,
      'seo_description': instance.seoDescription,
      'meta_keywords': instance.metaKeywords,
      'is_featured': instance.isFeatured,
      'is_digital': instance.isDigital,
      'requires_shipping': instance.requiresShipping,
      'taxable': instance.taxable,
      'reviews': instance.reviews.map((e) => e.toJson()).toList(),
      'average_rating': instance.averageRating,
      'total_reviews': instance.totalReviews,
      'related_product_ids': instance.relatedProductIds,
      'custom_fields': instance.customFields,
    };

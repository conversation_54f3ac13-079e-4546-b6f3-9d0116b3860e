import 'package:injectable/injectable.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../platform_integration/domain/entities/platform_product.dart';
import '../../../platform_integration/domain/repositories/platform_repository.dart';
import '../models/product_model.dart';
import '../../domain/repositories/product_repository.dart';
import 'product_remote_datasource.dart';

/// Implementation of ProductRemoteDataSource that uses the platform integration layer
@LazySingleton(as: ProductRemoteDataSource)
class PlatformProductRemoteDataSource implements ProductRemoteDataSource {
  const PlatformProductRemoteDataSource(this._platformRepository);

  final PlatformRepository _platformRepository;

  @override
  Future<List<ProductModel>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? query,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    final result = await _platformRepository.getProducts(
      page: page,
      limit: limit,
      collectionId: categoryId,
      query: query,
      filters: filter?.toMap(),
    );

    return result.fold(
      (failure) => throw ServerException(message: failure.message),
      (platformProducts) => platformProducts
          .map((product) => _mapPlatformProductToModel(product))
          .toList(),
    );
  }

  @override
  Future<ProductModel> getProduct(String productId) async {
    final result = await _platformRepository.getProduct(productId);

    return result.fold(
      (failure) => throw ServerException(message: failure.message),
      (platformProduct) => _mapPlatformProductToModel(platformProduct),
    );
  }

  @override
  Future<List<ProductModel>> searchProducts({
    required String query,
    int page = 1,
    int limit = 20,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    final result = await _platformRepository.searchProducts(
      query: query,
      page: page,
      limit: limit,
      filters: filter?.toMap(),
    );

    return result.fold(
      (failure) => throw ServerException(message: failure.message),
      (platformProducts) => platformProducts
          .map((product) => _mapPlatformProductToModel(product))
          .toList(),
    );
  }

  @override
  Future<List<ProductModel>> getProductsByCategory({
    required String categoryId,
    int page = 1,
    int limit = 20,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  }) async {
    final result = await _platformRepository.getProducts(
      page: page,
      limit: limit,
      collectionId: categoryId,
      filters: filter?.toMap(),
    );

    return result.fold(
      (failure) => throw ServerException(message: failure.message),
      (platformProducts) => platformProducts
          .map((product) => _mapPlatformProductToModel(product))
          .toList(),
    );
  }

  @override
  Future<List<ProductModel>> getFeaturedProducts({
    int page = 1,
    int limit = 20,
  }) async {
    // Use platform repository with featured filter
    final result = await _platformRepository.getProducts(
      page: page,
      limit: limit,
      filters: {'featured': true},
    );

    return result.fold(
      (failure) => throw ServerException(message: failure.message),
      (platformProducts) => platformProducts
          .map((product) => _mapPlatformProductToModel(product))
          .toList(),
    );
  }

  @override
  Future<List<ProductModel>> getRelatedProducts({
    required String productId,
    int limit = 10,
  }) async {
    final result = await _platformRepository.getRecommendedProducts(
      productId: productId,
      limit: limit,
    );

    return result.fold(
      (failure) => throw ServerException(message: failure.message),
      (platformProducts) => platformProducts
          .map((product) => _mapPlatformProductToModel(product))
          .toList(),
    );
  }

  @override
  Future<List<ProductModel>> getRecommendedProducts({
    String? userId,
    String? productId,
    int limit = 10,
  }) async {
    final result = await _platformRepository.getRecommendedProducts(
      productId: productId,
      limit: limit,
    );

    return result.fold(
      (failure) => throw ServerException(message: failure.message),
      (platformProducts) => platformProducts
          .map((product) => _mapPlatformProductToModel(product))
          .toList(),
    );
  }

  @override
  Future<List<ProductModel>> getProductsByIds(List<String> productIds) async {
    final products = <ProductModel>[];
    
    // Fetch products individually (could be optimized with batch API if available)
    for (final productId in productIds) {
      try {
        final product = await getProduct(productId);
        products.add(product);
      } catch (e) {
        // Continue with other products if one fails
        continue;
      }
    }

    return products;
  }

  /// Map platform product to product model
  ProductModel _mapPlatformProductToModel(PlatformProduct platformProduct) {
    return ProductModel(
      id: platformProduct.id,
      createdAt: platformProduct.createdAt,
      updatedAt: platformProduct.updatedAt,
      name: platformProduct.title,
      description: platformProduct.description,
      price: platformProduct.price,
      currency: platformProduct.currency,
      compareAtPrice: platformProduct.compareAtPrice,
      vendor: platformProduct.vendor,
      handle: platformProduct.handle,
      categoryId: 'default', // Platform products don't have category structure
      categoryName: platformProduct.productType ?? 'General',
      categorySlug: platformProduct.productType?.toLowerCase().replaceAll(' ', '-') ?? 'general',
      availabilityStatus: platformProduct.isAvailable ? 'in_stock' : 'out_of_stock',
      availabilityQuantity: platformProduct.isAvailable ? 999 : 0, // Platform doesn't provide quantity
      images: platformProduct.images.map((image) => _mapPlatformImageToModel(image)).toList(),
      variants: platformProduct.variants.map((variant) => _mapPlatformVariantToModel(variant)).toList(),
      tags: platformProduct.tags,
      seoTitle: platformProduct.seo?.title,
      seoDescription: platformProduct.seo?.description,
      metaKeywords: platformProduct.seo?.keywords ?? [],
      isFeatured: false, // Would need to be determined by platform-specific logic
      isDigital: false, // Would need to be determined by platform-specific logic
      requiresShipping: true, // Default assumption
      taxable: true, // Default assumption
    );
  }

  /// Map platform product image to product image model
  Map<String, dynamic> _mapPlatformImageToModel(dynamic platformImage) {
    // This would need to be implemented based on the actual PlatformProductImage structure
    return {
      'id': platformImage.id ?? '',
      'url': platformImage.url ?? '',
      'alt_text': platformImage.altText ?? '',
      'position': platformImage.position ?? 0,
      'width': platformImage.width,
      'height': platformImage.height,
      'is_main': platformImage.position == 0,
    };
  }

  /// Map platform product variant to product variant model
  Map<String, dynamic> _mapPlatformVariantToModel(dynamic platformVariant) {
    // This would need to be implemented based on the actual PlatformProductVariant structure
    return {
      'id': platformVariant.id ?? '',
      'product_id': platformVariant.productId ?? '',
      'title': platformVariant.title ?? '',
      'sku': platformVariant.sku,
      'price': platformVariant.price ?? 0.0,
      'currency': 'USD', // Would need to be determined from platform
      'availability_status': platformVariant.available ? 'in_stock' : 'out_of_stock',
      'availability_quantity': platformVariant.available ? 999 : 0,
      'options': platformVariant.options ?? <String, String>{},
      'position': platformVariant.position ?? 0,
      'requires_shipping': true,
      'taxable': true,
      'inventory_policy': 'deny',
      'fulfillment_service': 'manual',
      'images': <Map<String, dynamic>>[],
    };
  }
}

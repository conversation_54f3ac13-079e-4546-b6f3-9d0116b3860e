import 'package:flutter_test/flutter_test.dart';
import 'package:loc_store/features/products/domain/entities/product.dart';
import 'package:loc_store/features/products/domain/entities/product_image.dart';
import 'package:loc_store/features/products/domain/entities/product_variant.dart';
import 'package:loc_store/features/products/domain/value_objects/money.dart';
import 'package:loc_store/features/products/domain/value_objects/product_availability.dart';
import 'package:loc_store/features/products/domain/value_objects/product_category.dart';

void main() {
  group('Product', () {
    late Product testProduct;
    late ProductCategory testCategory;
    late ProductAvailability testAvailability;
    late List<ProductImage> testImages;
    late List<ProductVariant> testVariants;

    setUp(() {
      testCategory = const ProductCategory(
        id: 'cat1',
        name: 'Electronics',
        slug: 'electronics',
      );

      testAvailability = ProductAvailability.inStock(quantity: 10);

      testImages = [
        const ProductImage(
          id: 'img1',
          url: 'https://example.com/image1.jpg',
          altText: 'Product Image 1',
          isMain: true,
        ),
        const ProductImage(
          id: 'img2',
          url: 'https://example.com/image2.jpg',
          altText: 'Product Image 2',
        ),
      ];

      testVariants = [
        ProductVariant(
          id: 'var1',
          productId: 'prod1',
          title: 'Small Red',
          price: const Money(amount: 15.0, currency: 'USD'),
          availability: ProductAvailability.inStock(quantity: 5),
          options: const {'Size': 'Small', 'Color': 'Red'},
        ),
        ProductVariant(
          id: 'var2',
          productId: 'prod1',
          title: 'Large Blue',
          price: const Money(amount: 25.0, currency: 'USD'),
          availability: ProductAvailability.inStock(quantity: 3),
          options: const {'Size': 'Large', 'Color': 'Blue'},
        ),
      ];

      testProduct = Product(
        id: 'prod1',
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 2),
        name: 'Test Product',
        description: 'A test product description',
        price: const Money(amount: 20.0, currency: 'USD'),
        compareAtPrice: const Money(amount: 30.0, currency: 'USD'),
        category: testCategory,
        availability: testAvailability,
        images: testImages,
        variants: testVariants,
        tags: const ['electronics', 'gadget'],
        isFeatured: true,
        averageRating: 4.5,
        totalReviews: 10,
      );
    });

    group('constructor', () {
      test('should create Product with required fields', () {
        // arrange & act
        final product = Product(
          id: 'prod1',
          createdAt: DateTime(2023, 1, 1),
          updatedAt: DateTime(2023, 1, 2),
          name: 'Test Product',
          description: 'Description',
          price: const Money(amount: 10.0, currency: 'USD'),
          category: testCategory,
          availability: testAvailability,
          images: const [],
        );

        // assert
        expect(product.id, 'prod1');
        expect(product.name, 'Test Product');
        expect(product.price.amount, 10.0);
        expect(product.variants, isEmpty);
        expect(product.tags, isEmpty);
      });
    });

    group('main image', () {
      test('should return first image as main image', () {
        // act
        final mainImage = testProduct.mainImage;

        // assert
        expect(mainImage, isNotNull);
        expect(mainImage!.id, 'img1');
        expect(mainImage.isMain, true);
      });

      test('should return null when no images', () {
        // arrange
        final productWithoutImages = testProduct.copyWith(images: []);

        // act
        final mainImage = productWithoutImages.mainImage;

        // assert
        expect(mainImage, isNull);
      });
    });

    group('sale properties', () {
      test('should identify product on sale', () {
        // act & assert
        expect(testProduct.isOnSale, true);
        expect(testProduct.discountPercentage, closeTo(33.33, 0.01));
        expect(testProduct.savingsAmount?.amount, 10.0);
      });

      test('should identify product not on sale', () {
        // arrange
        final productNotOnSale = testProduct.copyWith(compareAtPrice: null);

        // act & assert
        expect(productNotOnSale.isOnSale, false);
        expect(productNotOnSale.discountPercentage, 0.0);
        expect(productNotOnSale.savingsAmount, isNull);
      });
    });

    group('variants', () {
      test('should identify product with variants', () {
        // act & assert
        expect(testProduct.hasVariants, true);
        expect(testProduct.availableVariants, hasLength(2));
      });

      test('should return correct price range', () {
        // act
        final priceRange = testProduct.priceRange;

        // assert
        expect(priceRange, isNotNull);
        expect(priceRange!.min.amount, 15.0);
        expect(priceRange.max.amount, 25.0);
      });

      test('should return display price as lowest variant price', () {
        // act
        final displayPrice = testProduct.displayPrice;

        // assert
        expect(displayPrice.amount, 15.0);
      });

      test('should format price range correctly', () {
        // act
        final formattedRange = testProduct.formattedPriceRange;

        // assert
        expect(formattedRange, '\$15.00 - \$25.00');
      });

      test('should return single price when all variants have same price', () {
        // arrange
        final samePrice = const Money(amount: 20.0, currency: 'USD');
        final variantsWithSamePrice = testVariants.map((v) => 
          v.copyWith(price: samePrice)).toList();
        final productWithSamePrice = testProduct.copyWith(variants: variantsWithSamePrice);

        // act
        final formattedRange = productWithSamePrice.formattedPriceRange;

        // assert
        expect(formattedRange, '\$20.00');
      });

      test('should get variant by ID', () {
        // act
        final variant = testProduct.getVariantById('var1');

        // assert
        expect(variant, isNotNull);
        expect(variant!.id, 'var1');
        expect(variant.title, 'Small Red');
      });

      test('should return null for non-existent variant ID', () {
        // act
        final variant = testProduct.getVariantById('nonexistent');

        // assert
        expect(variant, isNull);
      });

      test('should get variants by option', () {
        // act
        final redVariants = testProduct.getVariantsByOption('Color', 'Red');

        // assert
        expect(redVariants, hasLength(1));
        expect(redVariants.first.id, 'var1');
      });

      test('should get option values', () {
        // act
        final colorValues = testProduct.getOptionValues('Color');
        final sizeValues = testProduct.getOptionValues('Size');

        // assert
        expect(colorValues, containsAll(['Red', 'Blue']));
        expect(sizeValues, containsAll(['Small', 'Large']));
      });

      test('should get option names', () {
        // act
        final optionNames = testProduct.optionNames;

        // assert
        expect(optionNames, containsAll(['Color', 'Size']));
      });
    });

    group('availability', () {
      test('should be available when in stock', () {
        // act & assert
        expect(testProduct.isAvailable, true);
      });

      test('should be unavailable when out of stock', () {
        // arrange
        final outOfStockAvailability = ProductAvailability.outOfStock();
        final outOfStockProduct = testProduct.copyWith(availability: outOfStockAvailability);

        // act & assert
        expect(outOfStockProduct.isAvailable, false);
      });

      test('should be unavailable when no available variants', () {
        // arrange
        final outOfStockVariants = testVariants.map((v) => 
          v.copyWith(availability: ProductAvailability.outOfStock())).toList();
        final productWithOutOfStockVariants = testProduct.copyWith(variants: outOfStockVariants);

        // act & assert
        expect(productWithOutOfStockVariants.isAvailable, false);
      });
    });

    group('rating', () {
      test('should identify good rating', () {
        // act & assert
        expect(testProduct.hasGoodRating, true);
        expect(testProduct.ratingStars, 5);
      });

      test('should identify poor rating', () {
        // arrange
        final poorRatedProduct = testProduct.copyWith(averageRating: 2.5);

        // act & assert
        expect(poorRatedProduct.hasGoodRating, false);
        expect(poorRatedProduct.ratingStars, 3);
      });

      test('should handle no reviews', () {
        // arrange
        final unratedProduct = testProduct.copyWith(averageRating: 0.0, totalReviews: 0);

        // act & assert
        expect(unratedProduct.hasGoodRating, false);
        expect(unratedProduct.ratingStars, 0);
      });
    });

    group('copyWith', () {
      test('should create copy with modified name', () {
        // act
        final copy = testProduct.copyWith(name: 'Modified Product');

        // assert
        expect(copy.name, 'Modified Product');
        expect(copy.id, testProduct.id);
        expect(testProduct.name, 'Test Product'); // original unchanged
      });

      test('should create copy with modified price', () {
        // arrange
        const newPrice = Money(amount: 50.0, currency: 'USD');

        // act
        final copy = testProduct.copyWith(price: newPrice);

        // assert
        expect(copy.price, newPrice);
        expect(copy.name, testProduct.name);
      });
    });

    group('equality', () {
      test('should be equal when all properties are same', () {
        // arrange
        final product1 = testProduct;
        final product2 = testProduct.copyWith();

        // act & assert
        expect(product1, equals(product2));
        expect(product1.hashCode, equals(product2.hashCode));
      });

      test('should not be equal when properties differ', () {
        // arrange
        final product1 = testProduct;
        final product2 = testProduct.copyWith(name: 'Different Name');

        // act & assert
        expect(product1, isNot(equals(product2)));
      });
    });

    group('toString', () {
      test('should return formatted string', () {
        // act
        final string = testProduct.toString();

        // assert
        expect(string, contains('prod1'));
        expect(string, contains('Test Product'));
        expect(string, contains('\$20.00'));
      });
    });
  });
}

import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../entities/product.dart';
import '../repositories/product_repository.dart';

/// Use case for getting products with pagination and filtering
class GetProductsUseCase implements UseCase<List<Product>, GetProductsParams> {
  const GetProductsUseCase(this._repository);

  final ProductRepository _repository;

  @override
  Future<Either<Failure, List<Product>>> call(GetProductsParams params) async {
    return await _repository.getProducts(
      page: params.page,
      limit: params.limit,
      categoryId: params.categoryId,
      query: params.query,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
      filter: params.filter,
    );
  }
}

/// Parameters for getting products
class GetProductsParams extends PaginationParams {
  const GetProductsParams({
    super.page = 1,
    super.limit = 20,
    this.categoryId,
    this.query,
    this.sortBy,
    this.sortOrder = SortOrder.asc,
    this.filter,
  });

  final String? categoryId;
  final String? query;
  final ProductSortBy? sortBy;
  final SortOrder sortOrder;
  final ProductFilter? filter;

  @override
  String toString() => 'GetProductsParams('
      'page: $page, '
      'limit: $limit, '
      'categoryId: $categoryId, '
      'query: $query, '
      'sortBy: $sortBy, '
      'sortOrder: $sortOrder, '
      'filter: $filter)';
}

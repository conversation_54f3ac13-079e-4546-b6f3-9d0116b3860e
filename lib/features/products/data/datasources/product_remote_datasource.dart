import '../models/product_model.dart';
import '../../domain/repositories/product_repository.dart';

/// Abstract interface for product remote data source
abstract class ProductRemoteDataSource {
  /// Get products with pagination and filtering
  Future<List<ProductModel>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? query,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Get a single product by ID
  Future<ProductModel> getProduct(String productId);

  /// Search products
  Future<List<ProductModel>> searchProducts({
    required String query,
    int page = 1,
    int limit = 20,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Get products by category
  Future<List<ProductModel>> getProductsByCategory({
    required String categoryId,
    int page = 1,
    int limit = 20,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Get featured products
  Future<List<ProductModel>> getFeaturedProducts({
    int page = 1,
    int limit = 20,
  });

  /// Get related products
  Future<List<ProductModel>> getRelatedProducts({
    required String productId,
    int limit = 10,
  });

  /// Get product recommendations
  Future<List<ProductModel>> getRecommendedProducts({
    String? userId,
    String? productId,
    int limit = 10,
  });

  /// Get products by IDs
  Future<List<ProductModel>> getProductsByIds(List<String> productIds);
}

import 'package:equatable/equatable.dart';

/// Entity representing a product image
class ProductImage extends Equatable {
  const ProductImage({
    required this.id,
    required this.url,
    required this.altText,
    this.position = 0,
    this.width,
    this.height,
    this.thumbnailUrl,
    this.mediumUrl,
    this.largeUrl,
    this.isMain = false,
  });

  final String id;
  final String url;
  final String altText;
  final int position;
  final int? width;
  final int? height;
  final String? thumbnailUrl;
  final String? mediumUrl;
  final String? largeUrl;
  final bool isMain;

  /// Get the best available URL for a given size preference
  String getUrlForSize(ImageSize size) {
    switch (size) {
      case ImageSize.thumbnail:
        return thumbnailUrl ?? url;
      case ImageSize.medium:
        return mediumUrl ?? url;
      case ImageSize.large:
        return largeUrl ?? url;
      case ImageSize.original:
        return url;
    }
  }

  /// Check if image has dimensions
  bool get hasDimensions => width != null && height != null;

  /// Get aspect ratio if dimensions are available
  double? get aspectRatio {
    if (!hasDimensions) return null;
    return width! / height!;
  }

  /// Check if image is landscape
  bool get isLandscape => aspectRatio != null && aspectRatio! > 1.0;

  /// Check if image is portrait
  bool get isPortrait => aspectRatio != null && aspectRatio! < 1.0;

  /// Check if image is square
  bool get isSquare => aspectRatio != null && aspectRatio! == 1.0;

  /// Create a copy with modified values
  ProductImage copyWith({
    String? id,
    String? url,
    String? altText,
    int? position,
    int? width,
    int? height,
    String? thumbnailUrl,
    String? mediumUrl,
    String? largeUrl,
    bool? isMain,
  }) {
    return ProductImage(
      id: id ?? this.id,
      url: url ?? this.url,
      altText: altText ?? this.altText,
      position: position ?? this.position,
      width: width ?? this.width,
      height: height ?? this.height,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      mediumUrl: mediumUrl ?? this.mediumUrl,
      largeUrl: largeUrl ?? this.largeUrl,
      isMain: isMain ?? this.isMain,
    );
  }

  @override
  List<Object?> get props => [
        id,
        url,
        altText,
        position,
        width,
        height,
        thumbnailUrl,
        mediumUrl,
        largeUrl,
        isMain,
      ];

  @override
  String toString() => 'ProductImage(id: $id, url: $url, altText: $altText)';
}

/// Enumeration for image size preferences
enum ImageSize {
  thumbnail,
  medium,
  large,
  original,
}

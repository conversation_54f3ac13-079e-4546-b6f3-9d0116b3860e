import '../helpers/currency_helper.dart';

/// Int extension methods
extension IntExtensions on int {
  /// Convert to currency string
  String toCurrency({String symbol = '\$', int decimalPlaces = 2}) {
    return CurrencyHelper.formatCurrency(toDouble(), symbol: symbol, decimalPlaces: decimalPlaces);
  }

  /// Convert to ordinal string (1st, 2nd, 3rd, etc.)
  String get ordinal {
    if (this >= 11 && this <= 13) return '${this}th';

    switch (this % 10) {
      case 1:
        return '${this}st';
      case 2:
        return '${this}nd';
      case 3:
        return '${this}rd';
      default:
        return '${this}th';
    }
  }

  /// Check if number is even
  bool get isEven => this % 2 == 0;

  /// Check if number is odd
  bool get isOdd => this % 2 != 0;

  /// Check if number is positive
  bool get isPositive => this > 0;

  /// Check if number is negative
  bool get isNegative => this < 0;

  /// Check if number is zero
  bool get isZero => this == 0;

  /// Get absolute value
  int get absolute => abs();

  /// Convert to Roman numeral
  String get toRoman {
    if (this <= 0 || this > 3999) return toString();

    const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
    const symbols = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];

    var result = '';
    var number = this;

    for (var i = 0; i < values.length; i++) {
      while (number >= values[i]) {
        result += symbols[i];
        number -= values[i];
      }
    }

    return result;
  }

  /// Convert bytes to human readable format
  String get bytesToHumanReadable {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    var size = toDouble();
    var unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// Convert milliseconds to duration string
  String get millisecondsToTimeString {
    final duration = Duration(milliseconds: this);
    return duration.toTimeString;
  }

  /// Convert seconds to duration string
  String get secondsToTimeString {
    final duration = Duration(seconds: this);
    return duration.toTimeString;
  }

  /// Generate list of numbers from 0 to this
  List<int> get range => List.generate(this, (index) => index);

  /// Generate list of numbers from this to end
  List<int> rangeTo(int end) => List.generate(end - this + 1, (index) => this + index);

  /// Clamp value between min and max
  int clampTo(int min, int max) => clamp(min, max).toInt();
}

/// Double extension methods
extension DoubleExtensions on double {
  /// Convert to currency string
  String toCurrency({String symbol = '\$', int decimalPlaces = 2}) {
    return CurrencyHelper.formatCurrency(this, symbol: symbol, decimalPlaces: decimalPlaces);
  }

  /// Round to specified decimal places
  double roundToDecimalPlaces(int decimalPlaces) {
    return double.parse(toStringAsFixed(decimalPlaces));
  }

  /// Check if number is positive
  bool get isPositive => this > 0;

  /// Check if number is negative
  bool get isNegative => this < 0;

  /// Check if number is zero
  bool get isZero => this == 0.0;

  /// Get absolute value
  double get absolute => abs();

  /// Convert to percentage string
  String toPercentage({int decimalPlaces = 1}) {
    return '${(this * 100).toStringAsFixed(decimalPlaces)}%';
  }

  /// Convert to compact string (1.2K, 1.5M, etc.)
  String get toCompactString {
    if (this < 1000) return toStringAsFixed(0);
    if (this < 1000000) return '${(this / 1000).toStringAsFixed(1)}K';
    if (this < 1000000000) return '${(this / 1000000).toStringAsFixed(1)}M';
    return '${(this / 1000000000).toStringAsFixed(1)}B';
  }

  /// Check if number is approximately equal to another
  bool isApproximatelyEqual(double other, {double epsilon = 0.001}) {
    return (this - other).abs() < epsilon;
  }

  /// Clamp value between min and max
  double clampTo(double min, double max) => clamp(min, max).toDouble();

  /// Convert radians to degrees
  double get toDegrees => this * 180 / 3.14159265359;

  /// Convert degrees to radians
  double get toRadians => this * 3.14159265359 / 180;

  /// Format as file size
  String get toFileSize {
    return toInt().bytesToHumanReadable;
  }
}

/// Duration extension methods
extension DurationExtensions on Duration {
  /// Convert to human readable time string
  String get toTimeString {
    final hours = inHours;
    final minutes = inMinutes.remainder(60);
    final seconds = inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Convert to human readable duration string
  String get toHumanReadable {
    if (inDays > 0) {
      return '${inDays} day${inDays == 1 ? '' : 's'}';
    } else if (inHours > 0) {
      return '${inHours} hour${inHours == 1 ? '' : 's'}';
    } else if (inMinutes > 0) {
      return '${inMinutes} minute${inMinutes == 1 ? '' : 's'}';
    } else {
      return '${inSeconds} second${inSeconds == 1 ? '' : 's'}';
    }
  }

  /// Check if duration is positive
  bool get isPositive => inMicroseconds > 0;

  /// Check if duration is negative
  bool get isNegative => inMicroseconds < 0;

  /// Check if duration is zero
  bool get isZero => inMicroseconds == 0;

  /// Get absolute duration
  Duration get absolute => Duration(microseconds: inMicroseconds.abs());
}

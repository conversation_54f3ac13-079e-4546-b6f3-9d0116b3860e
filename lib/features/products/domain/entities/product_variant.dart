import 'package:equatable/equatable.dart';
import '../value_objects/money.dart';
import '../value_objects/product_availability.dart';
import 'product_image.dart';

/// Entity representing a product variant (e.g., different size, color, etc.)
class ProductVariant extends Equatable {
  const ProductVariant({
    required this.id,
    required this.productId,
    required this.title,
    required this.price,
    required this.availability,
    required this.options,
    this.sku,
    this.barcode,
    this.compareAtPrice,
    this.weight,
    this.images = const [],
    this.position = 0,
    this.requiresShipping = true,
    this.taxable = true,
    this.inventoryPolicy = InventoryPolicy.deny,
    this.fulfillmentService = 'manual',
  });

  final String id;
  final String productId;
  final String title;
  final String? sku;
  final String? barcode;
  final Money price;
  final Money? compareAtPrice;
  final ProductAvailability availability;
  final Map<String, String> options; // e.g., {'Color': 'Red', 'Size': 'Large'}
  final double? weight;
  final List<ProductImage> images;
  final int position;
  final bool requiresShipping;
  final bool taxable;
  final InventoryPolicy inventoryPolicy;
  final String fulfillmentService;

  /// Get the main variant image
  ProductImage? get mainImage => images.isNotEmpty ? images.first : null;

  /// Check if variant is on sale
  bool get isOnSale => compareAtPrice != null && compareAtPrice! > price;

  /// Get discount percentage if on sale
  double get discountPercentage {
    if (!isOnSale || compareAtPrice == null) return 0.0;
    return ((compareAtPrice!.amount - price.amount) / compareAtPrice!.amount) * 100;
  }

  /// Get savings amount if on sale
  Money? get savingsAmount {
    if (!isOnSale || compareAtPrice == null) return null;
    return compareAtPrice! - price;
  }

  /// Check if variant is available for purchase
  bool get isAvailable => availability.isAvailable;

  /// Get formatted options string (e.g., "Red / Large")
  String get formattedOptions {
    if (options.isEmpty) return '';
    return options.values.join(' / ');
  }

  /// Get option value by name
  String? getOptionValue(String optionName) => options[optionName];

  /// Check if variant has specific option
  bool hasOption(String optionName, String optionValue) =>
      options[optionName] == optionValue;

  /// Check if variant matches all given options
  bool matchesOptions(Map<String, String> targetOptions) {
    for (final entry in targetOptions.entries) {
      if (options[entry.key] != entry.value) return false;
    }
    return true;
  }

  /// Get display title (includes options if available)
  String get displayTitle {
    if (options.isEmpty) return title;
    return '$title - $formattedOptions';
  }

  /// Check if variant can fulfill a specific quantity
  bool canFulfillQuantity(int quantity) {
    return availability.canOrderQuantity(quantity);
  }

  /// Get maximum quantity that can be ordered
  int get maxOrderQuantity => availability.maxAvailableQuantity;

  /// Create a copy with modified values
  ProductVariant copyWith({
    String? id,
    String? productId,
    String? title,
    String? sku,
    String? barcode,
    Money? price,
    Money? compareAtPrice,
    ProductAvailability? availability,
    Map<String, String>? options,
    double? weight,
    List<ProductImage>? images,
    int? position,
    bool? requiresShipping,
    bool? taxable,
    InventoryPolicy? inventoryPolicy,
    String? fulfillmentService,
  }) {
    return ProductVariant(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      title: title ?? this.title,
      sku: sku ?? this.sku,
      barcode: barcode ?? this.barcode,
      price: price ?? this.price,
      compareAtPrice: compareAtPrice ?? this.compareAtPrice,
      availability: availability ?? this.availability,
      options: options ?? this.options,
      weight: weight ?? this.weight,
      images: images ?? this.images,
      position: position ?? this.position,
      requiresShipping: requiresShipping ?? this.requiresShipping,
      taxable: taxable ?? this.taxable,
      inventoryPolicy: inventoryPolicy ?? this.inventoryPolicy,
      fulfillmentService: fulfillmentService ?? this.fulfillmentService,
    );
  }

  @override
  List<Object?> get props => [
        id,
        productId,
        title,
        sku,
        barcode,
        price,
        compareAtPrice,
        availability,
        options,
        weight,
        images,
        position,
        requiresShipping,
        taxable,
        inventoryPolicy,
        fulfillmentService,
      ];

  @override
  String toString() => 'ProductVariant(id: $id, title: $title, price: $price, options: $options)';
}

/// Enumeration for inventory policy
enum InventoryPolicy {
  /// Deny orders when out of stock
  deny,
  /// Allow orders when out of stock (backorder)
  continue,
}

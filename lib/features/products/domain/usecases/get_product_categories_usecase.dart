import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../value_objects/product_category.dart';
import '../repositories/product_repository.dart';

/// Use case for getting product categories
class GetProductCategoriesUseCase implements UseCase<List<ProductCategory>, GetProductCategoriesParams> {
  const GetProductCategoriesUseCase(this._repository);

  final ProductRepository _repository;

  @override
  Future<Either<Failure, List<ProductCategory>>> call(GetProductCategoriesParams params) async {
    return await _repository.getCategories(
      page: params.page,
      limit: params.limit,
      parentId: params.parentId,
    );
  }
}

/// Parameters for getting product categories
class GetProductCategoriesParams extends PaginationParams {
  const GetProductCategoriesParams({
    super.page = 1,
    super.limit = 50,
    this.parentId,
  });

  final String? parentId;

  @override
  String toString() => 'GetProductCategoriesParams('
      'page: $page, '
      'limit: $limit, '
      'parentId: $parentId)';
}

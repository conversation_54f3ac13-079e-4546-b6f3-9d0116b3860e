import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../entities/product.dart';
import '../repositories/product_repository.dart';

/// Use case for searching products
@injectable
class SearchProductsUseCase implements UseCase<List<Product>, SearchProductsParams> {
  const SearchProductsUseCase(this._repository);

  final ProductRepository _repository;

  @override
  Future<Either<Failure, List<Product>>> call(SearchProductsParams params) async {
    // Validate search query
    if (params.query.trim().isEmpty) {
      return const Left(ValidationFailure(message: 'Search query cannot be empty'));
    }

    if (params.query.trim().length < 2) {
      return const Left(ValidationFailure(message: 'Search query must be at least 2 characters'));
    }

    return await _repository.searchProducts(
      query: params.query.trim(),
      page: params.page,
      limit: params.limit,
      categoryId: params.categoryId,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
      filter: params.filter,
    );
  }
}

/// Parameters for searching products
class SearchProductsParams extends PaginationParams {
  const SearchProductsParams({
    required this.query,
    super.page = 1,
    super.limit = 20,
    this.categoryId,
    this.sortBy,
    this.sortOrder = SortOrder.asc,
    this.filter,
  });

  final String query;
  final String? categoryId;
  final ProductSortBy? sortBy;
  final SortOrder sortOrder;
  final ProductFilter? filter;

  @override
  String toString() => 'SearchProductsParams('
      'query: $query, '
      'page: $page, '
      'limit: $limit, '
      'categoryId: $categoryId, '
      'sortBy: $sortBy, '
      'sortOrder: $sortOrder, '
      'filter: $filter)';
}

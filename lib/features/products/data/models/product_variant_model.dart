import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/product_variant.dart';
import '../../domain/value_objects/money.dart';
import '../../domain/value_objects/product_availability.dart';
import 'product_image_model.dart';

part 'product_variant_model.g.dart';

@JsonSerializable(explicitToJson: true)
class ProductVariantModel {
  const ProductVariantModel({
    required this.id,
    required this.productId,
    required this.title,
    required this.price,
    required this.currency,
    required this.availabilityStatus,
    required this.availabilityQuantity,
    required this.options,
    this.sku,
    this.barcode,
    this.compareAtPrice,
    this.weight,
    this.images = const [],
    this.position = 0,
    this.requiresShipping = true,
    this.taxable = true,
    this.inventoryPolicy = 'deny',
    this.fulfillmentService = 'manual',
    this.lowStockThreshold = 5,
    this.allowBackorder = false,
    this.estimatedRestockDate,
    this.maxOrderQuantity,
    this.minOrderQuantity = 1,
  });

  final String id;
  @JsonKey(name: 'product_id')
  final String productId;
  final String title;
  final String? sku;
  final String? barcode;
  final double price;
  final String currency;
  @JsonKey(name: 'compare_at_price')
  final double? compareAtPrice;
  @JsonKey(name: 'availability_status')
  final String availabilityStatus;
  @JsonKey(name: 'availability_quantity')
  final int availabilityQuantity;
  @JsonKey(name: 'low_stock_threshold')
  final int lowStockThreshold;
  @JsonKey(name: 'allow_backorder')
  final bool allowBackorder;
  @JsonKey(name: 'estimated_restock_date')
  final DateTime? estimatedRestockDate;
  @JsonKey(name: 'max_order_quantity')
  final int? maxOrderQuantity;
  @JsonKey(name: 'min_order_quantity')
  final int minOrderQuantity;
  final Map<String, String> options;
  final double? weight;
  final List<ProductImageModel> images;
  final int position;
  @JsonKey(name: 'requires_shipping')
  final bool requiresShipping;
  final bool taxable;
  @JsonKey(name: 'inventory_policy')
  final String inventoryPolicy;
  @JsonKey(name: 'fulfillment_service')
  final String fulfillmentService;

  factory ProductVariantModel.fromJson(Map<String, dynamic> json) =>
      _$ProductVariantModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProductVariantModelToJson(this);

  /// Convert model to domain entity
  ProductVariant toEntity() {
    return ProductVariant(
      id: id,
      productId: productId,
      title: title,
      sku: sku,
      barcode: barcode,
      price: Money(amount: price, currency: currency),
      compareAtPrice: compareAtPrice != null
          ? Money(amount: compareAtPrice!, currency: currency)
          : null,
      availability: _mapAvailability(),
      options: options,
      weight: weight,
      images: images.map((model) => model.toEntity()).toList(),
      position: position,
      requiresShipping: requiresShipping,
      taxable: taxable,
      inventoryPolicy: _mapInventoryPolicy(),
      fulfillmentService: fulfillmentService,
    );
  }

  /// Create model from domain entity
  factory ProductVariantModel.fromEntity(ProductVariant entity) {
    return ProductVariantModel(
      id: entity.id,
      productId: entity.productId,
      title: entity.title,
      sku: entity.sku,
      barcode: entity.barcode,
      price: entity.price.amount,
      currency: entity.price.currency,
      compareAtPrice: entity.compareAtPrice?.amount,
      availabilityStatus: _mapAvailabilityStatusToString(entity.availability.status),
      availabilityQuantity: entity.availability.quantity,
      lowStockThreshold: entity.availability.lowStockThreshold,
      allowBackorder: entity.availability.allowBackorder,
      estimatedRestockDate: entity.availability.estimatedRestockDate,
      maxOrderQuantity: entity.availability.maxOrderQuantity,
      minOrderQuantity: entity.availability.minOrderQuantity,
      options: entity.options,
      weight: entity.weight,
      images: entity.images.map((image) => ProductImageModel.fromEntity(image)).toList(),
      position: entity.position,
      requiresShipping: entity.requiresShipping,
      taxable: entity.taxable,
      inventoryPolicy: _mapInventoryPolicyToString(entity.inventoryPolicy),
      fulfillmentService: entity.fulfillmentService,
    );
  }

  /// Map availability from model data
  ProductAvailability _mapAvailability() {
    final status = _mapAvailabilityStatusFromString(availabilityStatus);

    return ProductAvailability(
      status: status,
      quantity: availabilityQuantity,
      lowStockThreshold: lowStockThreshold,
      allowBackorder: allowBackorder,
      estimatedRestockDate: estimatedRestockDate,
      maxOrderQuantity: maxOrderQuantity,
      minOrderQuantity: minOrderQuantity,
    );
  }

  /// Map inventory policy from string
  InventoryPolicy _mapInventoryPolicy() {
    switch (inventoryPolicy.toLowerCase()) {
      case 'continue':
        return InventoryPolicy.continueOrdering;
      case 'deny':
      default:
        return InventoryPolicy.deny;
    }
  }

  /// Map availability status from string
  AvailabilityStatus _mapAvailabilityStatusFromString(String status) {
    switch (status.toLowerCase()) {
      case 'in_stock':
        return AvailabilityStatus.inStock;
      case 'out_of_stock':
        return AvailabilityStatus.outOfStock;
      case 'low_stock':
        return AvailabilityStatus.lowStock;
      case 'pre_order':
        return AvailabilityStatus.preOrder;
      case 'discontinued':
        return AvailabilityStatus.discontinued;
      case 'back_order':
        return AvailabilityStatus.backOrder;
      default:
        return AvailabilityStatus.outOfStock;
    }
  }

  /// Map availability status to string
  static String _mapAvailabilityStatusToString(AvailabilityStatus status) {
    switch (status) {
      case AvailabilityStatus.inStock:
        return 'in_stock';
      case AvailabilityStatus.outOfStock:
        return 'out_of_stock';
      case AvailabilityStatus.lowStock:
        return 'low_stock';
      case AvailabilityStatus.preOrder:
        return 'pre_order';
      case AvailabilityStatus.discontinued:
        return 'discontinued';
      case AvailabilityStatus.backOrder:
        return 'back_order';
    }
  }

  /// Map inventory policy to string
  static String _mapInventoryPolicyToString(InventoryPolicy policy) {
    switch (policy) {
      case InventoryPolicy.continueOrdering:
        return 'continue';
      case InventoryPolicy.deny:
        return 'deny';
    }
  }
}

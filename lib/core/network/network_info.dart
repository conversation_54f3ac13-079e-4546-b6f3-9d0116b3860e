import 'package:injectable/injectable.dart';

/// Interface for checking network connectivity
abstract class NetworkInfo {
  /// Check if the device is connected to the internet
  Future<bool> get isConnected;

  /// Stream of connectivity changes
  Stream<bool> get onConnectivityChanged;
}

/// Implementation of NetworkInfo using connectivity_plus package
/// Note: This will be implemented after adding connectivity_plus dependency
@LazySingleton(as: NetworkInfo)
class NetworkInfoImpl implements NetworkInfo {
  @override
  Future<bool> get isConnected async {
    // TODO: Implement using connectivity_plus package
    // For now, return true as a placeholder
    return true;
  }

  @override
  Stream<bool> get onConnectivityChanged {
    // TODO: Implement using connectivity_plus package
    // For now, return a stream that emits true
    return Stream.value(true);
  }
}

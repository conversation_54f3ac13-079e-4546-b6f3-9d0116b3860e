import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/product_image.dart';
import '../../domain/entities/product_variant.dart';
import '../../domain/entities/product_review.dart';
import '../../domain/value_objects/money.dart';
import '../../domain/value_objects/product_availability.dart';
import '../../domain/value_objects/product_category.dart';
import 'product_image_model.dart';
import 'product_variant_model.dart';
import 'product_review_model.dart';

part 'product_model.g.dart';

@JsonSerializable(explicitToJson: true)
class ProductModel {
  const ProductModel({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.categoryId,
    required this.categoryName,
    required this.categorySlug,
    required this.availabilityStatus,
    required this.availabilityQuantity,
    required this.images,
    this.shortDescription,
    this.sku,
    this.brand,
    this.weight,
    this.dimensions,
    this.variants = const [],
    this.tags = const [],
    this.compareAtPrice,
    this.vendor,
    this.handle,
    this.seoTitle,
    this.seoDescription,
    this.metaKeywords = const [],
    this.isFeatured = false,
    this.isDigital = false,
    this.requiresShipping = true,
    this.taxable = true,
    this.reviews = const [],
    this.averageRating = 0.0,
    this.totalReviews = 0,
    this.relatedProductIds = const [],
    this.customFields = const {},
    this.lowStockThreshold = 5,
    this.allowBackorder = false,
    this.estimatedRestockDate,
    this.maxOrderQuantity,
    this.minOrderQuantity = 1,
    this.categoryDescription,
    this.categoryParentId,
    this.categoryImageUrl,
    this.categorySortOrder = 0,
    this.categoryIsActive = true,
  });

  final String id;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  final String name;
  final String description;
  @JsonKey(name: 'short_description')
  final String? shortDescription;
  final String? sku;
  final String? brand;
  final double price;
  final String currency;
  @JsonKey(name: 'compare_at_price')
  final double? compareAtPrice;
  final String? vendor;
  final String? handle;
  @JsonKey(name: 'category_id')
  final String categoryId;
  @JsonKey(name: 'category_name')
  final String categoryName;
  @JsonKey(name: 'category_slug')
  final String categorySlug;
  @JsonKey(name: 'category_description')
  final String? categoryDescription;
  @JsonKey(name: 'category_parent_id')
  final String? categoryParentId;
  @JsonKey(name: 'category_image_url')
  final String? categoryImageUrl;
  @JsonKey(name: 'category_sort_order')
  final int categorySortOrder;
  @JsonKey(name: 'category_is_active')
  final bool categoryIsActive;
  @JsonKey(name: 'availability_status')
  final String availabilityStatus;
  @JsonKey(name: 'availability_quantity')
  final int availabilityQuantity;
  @JsonKey(name: 'low_stock_threshold')
  final int lowStockThreshold;
  @JsonKey(name: 'allow_backorder')
  final bool allowBackorder;
  @JsonKey(name: 'estimated_restock_date')
  final DateTime? estimatedRestockDate;
  @JsonKey(name: 'max_order_quantity')
  final int? maxOrderQuantity;
  @JsonKey(name: 'min_order_quantity')
  final int minOrderQuantity;
  final List<ProductImageModel> images;
  final List<ProductVariantModel> variants;
  final List<String> tags;
  final double? weight;
  final Map<String, double>? dimensions;
  @JsonKey(name: 'seo_title')
  final String? seoTitle;
  @JsonKey(name: 'seo_description')
  final String? seoDescription;
  @JsonKey(name: 'meta_keywords')
  final List<String> metaKeywords;
  @JsonKey(name: 'is_featured')
  final bool isFeatured;
  @JsonKey(name: 'is_digital')
  final bool isDigital;
  @JsonKey(name: 'requires_shipping')
  final bool requiresShipping;
  final bool taxable;
  final List<ProductReviewModel> reviews;
  @JsonKey(name: 'average_rating')
  final double averageRating;
  @JsonKey(name: 'total_reviews')
  final int totalReviews;
  @JsonKey(name: 'related_product_ids')
  final List<String> relatedProductIds;
  @JsonKey(name: 'custom_fields')
  final Map<String, dynamic> customFields;

  factory ProductModel.fromJson(Map<String, dynamic> json) => _$ProductModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProductModelToJson(this);

  /// Convert model to domain entity
  Product toEntity() {
    return Product(
      id: id,
      createdAt: createdAt,
      updatedAt: updatedAt,
      name: name,
      description: description,
      shortDescription: shortDescription,
      sku: sku,
      brand: brand,
      price: Money(amount: price, currency: currency),
      compareAtPrice: compareAtPrice != null 
          ? Money(amount: compareAtPrice!, currency: currency)
          : null,
      vendor: vendor,
      handle: handle,
      category: ProductCategory(
        id: categoryId,
        name: categoryName,
        slug: categorySlug,
        description: categoryDescription,
        parentId: categoryParentId,
        imageUrl: categoryImageUrl,
        sortOrder: categorySortOrder,
        isActive: categoryIsActive,
      ),
      availability: _mapAvailability(),
      images: images.map((model) => model.toEntity()).toList(),
      variants: variants.map((model) => model.toEntity()).toList(),
      tags: tags,
      weight: weight,
      dimensions: dimensions,
      seoTitle: seoTitle,
      seoDescription: seoDescription,
      metaKeywords: metaKeywords,
      isFeatured: isFeatured,
      isDigital: isDigital,
      requiresShipping: requiresShipping,
      taxable: taxable,
      reviews: reviews.map((model) => model.toEntity()).toList(),
      averageRating: averageRating,
      totalReviews: totalReviews,
      relatedProductIds: relatedProductIds,
      customFields: customFields,
    );
  }

  /// Create model from domain entity
  factory ProductModel.fromEntity(Product entity) {
    return ProductModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      name: entity.name,
      description: entity.description,
      shortDescription: entity.shortDescription,
      sku: entity.sku,
      brand: entity.brand,
      price: entity.price.amount,
      currency: entity.price.currency,
      compareAtPrice: entity.compareAtPrice?.amount,
      vendor: entity.vendor,
      handle: entity.handle,
      categoryId: entity.category.id,
      categoryName: entity.category.name,
      categorySlug: entity.category.slug,
      categoryDescription: entity.category.description,
      categoryParentId: entity.category.parentId,
      categoryImageUrl: entity.category.imageUrl,
      categorySortOrder: entity.category.sortOrder,
      categoryIsActive: entity.category.isActive,
      availabilityStatus: _mapAvailabilityStatusToString(entity.availability.status),
      availabilityQuantity: entity.availability.quantity,
      lowStockThreshold: entity.availability.lowStockThreshold,
      allowBackorder: entity.availability.allowBackorder,
      estimatedRestockDate: entity.availability.estimatedRestockDate,
      maxOrderQuantity: entity.availability.maxOrderQuantity,
      minOrderQuantity: entity.availability.minOrderQuantity,
      images: entity.images.map((image) => ProductImageModel.fromEntity(image)).toList(),
      variants: entity.variants.map((variant) => ProductVariantModel.fromEntity(variant)).toList(),
      tags: entity.tags,
      weight: entity.weight,
      dimensions: entity.dimensions,
      seoTitle: entity.seoTitle,
      seoDescription: entity.seoDescription,
      metaKeywords: entity.metaKeywords,
      isFeatured: entity.isFeatured,
      isDigital: entity.isDigital,
      requiresShipping: entity.requiresShipping,
      taxable: entity.taxable,
      reviews: entity.reviews.map((review) => ProductReviewModel.fromEntity(review)).toList(),
      averageRating: entity.averageRating,
      totalReviews: entity.totalReviews,
      relatedProductIds: entity.relatedProductIds,
      customFields: entity.customFields,
    );
  }

  /// Map availability from model data
  ProductAvailability _mapAvailability() {
    final status = _mapAvailabilityStatusFromString(availabilityStatus);
    
    return ProductAvailability(
      status: status,
      quantity: availabilityQuantity,
      lowStockThreshold: lowStockThreshold,
      allowBackorder: allowBackorder,
      estimatedRestockDate: estimatedRestockDate,
      maxOrderQuantity: maxOrderQuantity,
      minOrderQuantity: minOrderQuantity,
    );
  }

  /// Map availability status from string
  AvailabilityStatus _mapAvailabilityStatusFromString(String status) {
    switch (status.toLowerCase()) {
      case 'in_stock':
        return AvailabilityStatus.inStock;
      case 'out_of_stock':
        return AvailabilityStatus.outOfStock;
      case 'low_stock':
        return AvailabilityStatus.lowStock;
      case 'pre_order':
        return AvailabilityStatus.preOrder;
      case 'discontinued':
        return AvailabilityStatus.discontinued;
      case 'back_order':
        return AvailabilityStatus.backOrder;
      default:
        return AvailabilityStatus.outOfStock;
    }
  }

  /// Map availability status to string
  static String _mapAvailabilityStatusToString(AvailabilityStatus status) {
    switch (status) {
      case AvailabilityStatus.inStock:
        return 'in_stock';
      case AvailabilityStatus.outOfStock:
        return 'out_of_stock';
      case AvailabilityStatus.lowStock:
        return 'low_stock';
      case AvailabilityStatus.preOrder:
        return 'pre_order';
      case AvailabilityStatus.discontinued:
        return 'discontinued';
      case AvailabilityStatus.backOrder:
        return 'back_order';
    }
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_review_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductReviewModel _$ProductReviewModelFromJson(Map<String, dynamic> json) =>
    ProductReviewModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      productId: json['product_id'] as String,
      userId: json['user_id'] as String,
      userName: json['user_name'] as String,
      rating: (json['rating'] as num).toInt(),
      title: json['title'] as String,
      content: json['content'] as String,
      userEmail: json['user_email'] as String?,
      userAvatar: json['user_avatar'] as String?,
      isVerifiedPurchase: json['is_verified_purchase'] as bool? ?? false,
      isApproved: json['is_approved'] as bool? ?? true,
      helpfulCount: (json['helpful_count'] as num?)?.toInt() ?? 0,
      notHelpfulCount: (json['not_helpful_count'] as num?)?.toInt() ?? 0,
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      variantId: json['variant_id'] as String?,
      variantTitle: json['variant_title'] as String?,
    );

Map<String, dynamic> _$ProductReviewModelToJson(ProductReviewModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'product_id': instance.productId,
      'user_id': instance.userId,
      'user_name': instance.userName,
      'user_email': instance.userEmail,
      'user_avatar': instance.userAvatar,
      'rating': instance.rating,
      'title': instance.title,
      'content': instance.content,
      'is_verified_purchase': instance.isVerifiedPurchase,
      'is_approved': instance.isApproved,
      'helpful_count': instance.helpfulCount,
      'not_helpful_count': instance.notHelpfulCount,
      'images': instance.images,
      'variant_id': instance.variantId,
      'variant_title': instance.variantTitle,
    };

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ecommerce_app/core/errors/failures.dart';
import 'package:ecommerce_app/features/platform_integration/domain/entities/platform_product.dart';
import 'package:ecommerce_app/features/platform_integration/domain/repositories/platform_repository.dart';
import 'package:ecommerce_app/features/platform_integration/domain/usecases/get_products_usecase.dart';

class MockPlatformRepository extends Mock implements PlatformRepository {}

void main() {
  late GetProductsUseCase useCase;
  late MockPlatformRepository mockRepository;

  setUp(() {
    mockRepository = MockPlatformRepository();
    useCase = GetProductsUseCase(mockRepository);
  });

  group('GetProductsUseCase', () {
    final tProducts = [
      PlatformProduct(
        id: '1',
        title: 'Test Product 1',
        description: 'Description 1',
        price: 99.99,
        currency: 'USD',
        images: const [],
        variants: const [],
        isAvailable: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      PlatformProduct(
        id: '2',
        title: 'Test Product 2',
        description: 'Description 2',
        price: 149.99,
        currency: 'USD',
        images: const [],
        variants: const [],
        isAvailable: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    const tParams = GetProductsParams(
      page: 1,
      limit: 20,
      collectionId: 'collection-1',
      query: 'test',
    );

    test('should get products from repository', () async {
      // arrange
      when(() => mockRepository.getProducts(
            page: any(named: 'page'),
            limit: any(named: 'limit'),
            collectionId: any(named: 'collectionId'),
            query: any(named: 'query'),
            filters: any(named: 'filters'),
          )).thenAnswer((_) async => Right<Failure, List<PlatformProduct>>(tProducts));

      // act
      final result = await useCase(tParams);

      // assert
      expect(result, Right<Failure, List<PlatformProduct>>(tProducts));
      verify(() => mockRepository.getProducts(
            page: tParams.page,
            limit: tParams.limit,
            collectionId: tParams.collectionId,
            query: tParams.query,
            filters: tParams.filters,
          ));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return failure when repository fails', () async {
      // arrange
      const tFailure = ServerFailure(message: 'Server error');
      when(() => mockRepository.getProducts(
            page: any(named: 'page'),
            limit: any(named: 'limit'),
            collectionId: any(named: 'collectionId'),
            query: any(named: 'query'),
            filters: any(named: 'filters'),
          )).thenAnswer((_) async => const Left<Failure, List<PlatformProduct>>(tFailure));

      // act
      final result = await useCase(tParams);

      // assert
      expect(result, const Left<Failure, List<PlatformProduct>>(tFailure));
      verify(() => mockRepository.getProducts(
            page: tParams.page,
            limit: tParams.limit,
            collectionId: tParams.collectionId,
            query: tParams.query,
            filters: tParams.filters,
          ));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should use default parameters when not provided', () async {
      // arrange
      const tDefaultParams = GetProductsParams();
      when(() => mockRepository.getProducts(
            page: any(named: 'page'),
            limit: any(named: 'limit'),
            collectionId: any(named: 'collectionId'),
            query: any(named: 'query'),
            filters: any(named: 'filters'),
          )).thenAnswer((_) async => Right<Failure, List<PlatformProduct>>(tProducts));

      // act
      final result = await useCase(tDefaultParams);

      // assert
      expect(result, Right<Failure, List<PlatformProduct>>(tProducts));
      verify(() => mockRepository.getProducts(
            page: 1,
            limit: 20,
            collectionId: null,
            query: null,
            filters: null,
          ));
    });
  });
}

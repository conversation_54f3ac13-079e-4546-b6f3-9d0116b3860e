import 'package:flutter_test/flutter_test.dart';
import 'package:loc_store/features/products/domain/value_objects/money.dart';

void main() {
  group('Money', () {
    group('constructor', () {
      test('should create Money with amount and currency', () {
        // arrange & act
        const money = Money(amount: 10.50, currency: 'USD');

        // assert
        expect(money.amount, 10.50);
        expect(money.currency, 'USD');
      });

      test('should create Money from double', () {
        // arrange & act
        const money = Money.fromDouble(25.99, 'EUR');

        // assert
        expect(money.amount, 25.99);
        expect(money.currency, 'EUR');
      });

      test('should create Money from cents', () {
        // arrange & act
        final money = Money.fromCents(1050, 'USD');

        // assert
        expect(money.amount, 10.50);
        expect(money.currency, 'USD');
      });

      test('should create zero Money', () {
        // arrange & act
        const money = Money.zero('GBP');

        // assert
        expect(money.amount, 0.0);
        expect(money.currency, 'GBP');
        expect(money.isZero, true);
      });
    });

    group('properties', () {
      test('should return correct amount in cents', () {
        // arrange
        const money = Money(amount: 10.50, currency: 'USD');

        // act & assert
        expect(money.amountInCents, 1050);
      });

      test('should identify positive amounts', () {
        // arrange
        const money = Money(amount: 5.0, currency: 'USD');

        // act & assert
        expect(money.isPositive, true);
        expect(money.isNegative, false);
        expect(money.isZero, false);
      });

      test('should identify negative amounts', () {
        // arrange
        const money = Money(amount: -5.0, currency: 'USD');

        // act & assert
        expect(money.isPositive, false);
        expect(money.isNegative, true);
        expect(money.isZero, false);
      });

      test('should identify zero amounts', () {
        // arrange
        const money = Money(amount: 0.0, currency: 'USD');

        // act & assert
        expect(money.isPositive, false);
        expect(money.isNegative, false);
        expect(money.isZero, true);
      });
    });

    group('arithmetic operations', () {
      test('should add money with same currency', () {
        // arrange
        const money1 = Money(amount: 10.0, currency: 'USD');
        const money2 = Money(amount: 5.0, currency: 'USD');

        // act
        final result = money1 + money2;

        // assert
        expect(result.amount, 15.0);
        expect(result.currency, 'USD');
      });

      test('should throw error when adding different currencies', () {
        // arrange
        const money1 = Money(amount: 10.0, currency: 'USD');
        const money2 = Money(amount: 5.0, currency: 'EUR');

        // act & assert
        expect(() => money1 + money2, throwsArgumentError);
      });

      test('should subtract money with same currency', () {
        // arrange
        const money1 = Money(amount: 10.0, currency: 'USD');
        const money2 = Money(amount: 3.0, currency: 'USD');

        // act
        final result = money1 - money2;

        // assert
        expect(result.amount, 7.0);
        expect(result.currency, 'USD');
      });

      test('should multiply money by factor', () {
        // arrange
        const money = Money(amount: 10.0, currency: 'USD');

        // act
        final result = money * 2.5;

        // assert
        expect(result.amount, 25.0);
        expect(result.currency, 'USD');
      });

      test('should divide money by factor', () {
        // arrange
        const money = Money(amount: 10.0, currency: 'USD');

        // act
        final result = money / 2.0;

        // assert
        expect(result.amount, 5.0);
        expect(result.currency, 'USD');
      });

      test('should throw error when dividing by zero', () {
        // arrange
        const money = Money(amount: 10.0, currency: 'USD');

        // act & assert
        expect(() => money / 0, throwsArgumentError);
      });
    });

    group('comparison operations', () {
      test('should compare money with same currency', () {
        // arrange
        const money1 = Money(amount: 10.0, currency: 'USD');
        const money2 = Money(amount: 5.0, currency: 'USD');
        const money3 = Money(amount: 10.0, currency: 'USD');

        // act & assert
        expect(money1 > money2, true);
        expect(money2 < money1, true);
        expect(money1 >= money3, true);
        expect(money1 <= money3, true);
      });

      test('should throw error when comparing different currencies', () {
        // arrange
        const money1 = Money(amount: 10.0, currency: 'USD');
        const money2 = Money(amount: 5.0, currency: 'EUR');

        // act & assert
        expect(() => money1 > money2, throwsArgumentError);
        expect(() => money1 < money2, throwsArgumentError);
      });
    });

    group('formatting', () {
      test('should format USD with default settings', () {
        // arrange
        const money = Money(amount: 10.50, currency: 'USD');

        // act
        final formatted = money.format();

        // assert
        expect(formatted, '\$10.50');
      });

      test('should format EUR with symbol', () {
        // arrange
        const money = Money(amount: 25.99, currency: 'EUR');

        // act
        final formatted = money.format();

        // assert
        expect(formatted, '€25.99');
      });

      test('should format VND with symbol', () {
        // arrange
        const money = Money(amount: 100000, currency: 'VND');

        // act
        final formatted = money.format(decimalPlaces: 0);

        // assert
        expect(formatted, '₫100000');
      });

      test('should format without symbol', () {
        // arrange
        const money = Money(amount: 10.50, currency: 'USD');

        // act
        final formatted = money.format(showSymbol: false);

        // assert
        expect(formatted, '10.50');
      });

      test('should format with custom decimal places', () {
        // arrange
        const money = Money(amount: 10.5, currency: 'USD');

        // act
        final formatted = money.format(decimalPlaces: 3);

        // assert
        expect(formatted, '\$10.500');
      });

      test('should format unknown currency with code', () {
        // arrange
        const money = Money(amount: 10.50, currency: 'XYZ');

        // act
        final formatted = money.format();

        // assert
        expect(formatted, 'XYZ 10.50');
      });
    });

    group('copyWith', () {
      test('should create copy with modified amount', () {
        // arrange
        const original = Money(amount: 10.0, currency: 'USD');

        // act
        final copy = original.copyWith(amount: 20.0);

        // assert
        expect(copy.amount, 20.0);
        expect(copy.currency, 'USD');
        expect(original.amount, 10.0); // original unchanged
      });

      test('should create copy with modified currency', () {
        // arrange
        const original = Money(amount: 10.0, currency: 'USD');

        // act
        final copy = original.copyWith(currency: 'EUR');

        // assert
        expect(copy.amount, 10.0);
        expect(copy.currency, 'EUR');
        expect(original.currency, 'USD'); // original unchanged
      });
    });

    group('equality', () {
      test('should be equal when amount and currency are same', () {
        // arrange
        const money1 = Money(amount: 10.0, currency: 'USD');
        const money2 = Money(amount: 10.0, currency: 'USD');

        // act & assert
        expect(money1, equals(money2));
        expect(money1.hashCode, equals(money2.hashCode));
      });

      test('should not be equal when amount differs', () {
        // arrange
        const money1 = Money(amount: 10.0, currency: 'USD');
        const money2 = Money(amount: 5.0, currency: 'USD');

        // act & assert
        expect(money1, isNot(equals(money2)));
      });

      test('should not be equal when currency differs', () {
        // arrange
        const money1 = Money(amount: 10.0, currency: 'USD');
        const money2 = Money(amount: 10.0, currency: 'EUR');

        // act & assert
        expect(money1, isNot(equals(money2)));
      });
    });

    group('toString', () {
      test('should return formatted string', () {
        // arrange
        const money = Money(amount: 10.50, currency: 'USD');

        // act
        final string = money.toString();

        // assert
        expect(string, '\$10.50');
      });
    });
  });
}

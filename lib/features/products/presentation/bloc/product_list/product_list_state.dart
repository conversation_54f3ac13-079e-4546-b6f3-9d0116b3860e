part of 'product_list_bloc.dart';

enum ProductListStatus {
  initial,
  loading,
  loadingMore,
  success,
  failure,
}

class ProductListState extends Equatable {
  const ProductListState({
    this.status = ProductListStatus.initial,
    this.products = const [],
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.searchQuery = '',
    this.isSearchMode = false,
    this.categoryId,
    this.sortBy,
    this.sortOrder = SortOrder.asc,
    this.filter,
    this.viewMode = ProductViewMode.grid,
    this.errorMessage,
  });

  final ProductListStatus status;
  final List<Product> products;
  final bool hasReachedMax;
  final int currentPage;
  final String searchQuery;
  final bool isSearchMode;
  final String? categoryId;
  final ProductSortBy? sortBy;
  final SortOrder sortOrder;
  final ProductFilter? filter;
  final ProductViewMode viewMode;
  final String? errorMessage;

  /// Check if the list is empty
  bool get isEmpty => products.isEmpty;

  /// Check if currently loading
  bool get isLoading => status == ProductListStatus.loading;

  /// Check if loading more items
  bool get isLoadingMore => status == ProductListStatus.loadingMore;

  /// Check if in success state
  bool get isSuccess => status == ProductListStatus.success;

  /// Check if in failure state
  bool get isFailure => status == ProductListStatus.failure;

  /// Check if can load more items
  bool get canLoadMore => !hasReachedMax && !isLoadingMore && isSuccess;

  /// Get filtered products count
  int get productsCount => products.length;

  /// Check if any filters are applied
  bool get hasFilters => filter != null && !filter!.isEmpty;

  /// Check if sorting is applied
  bool get hasSorting => sortBy != null;

  /// Get display title based on current state
  String get displayTitle {
    if (isSearchMode && searchQuery.isNotEmpty) {
      return 'Search results for "$searchQuery"';
    }
    if (categoryId != null) {
      return 'Category Products';
    }
    return 'All Products';
  }

  /// Get status message for display
  String? get statusMessage {
    switch (status) {
      case ProductListStatus.initial:
        return null;
      case ProductListStatus.loading:
        return 'Loading products...';
      case ProductListStatus.loadingMore:
        return 'Loading more products...';
      case ProductListStatus.success:
        if (isEmpty) {
          if (isSearchMode) {
            return 'No products found for "$searchQuery"';
          }
          return 'No products available';
        }
        return null;
      case ProductListStatus.failure:
        return errorMessage ?? 'Failed to load products';
    }
  }

  ProductListState copyWith({
    ProductListStatus? status,
    List<Product>? products,
    bool? hasReachedMax,
    int? currentPage,
    String? searchQuery,
    bool? isSearchMode,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder? sortOrder,
    ProductFilter? filter,
    ProductViewMode? viewMode,
    String? errorMessage,
  }) {
    return ProductListState(
      status: status ?? this.status,
      products: products ?? this.products,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      searchQuery: searchQuery ?? this.searchQuery,
      isSearchMode: isSearchMode ?? this.isSearchMode,
      categoryId: categoryId ?? this.categoryId,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      filter: filter ?? this.filter,
      viewMode: viewMode ?? this.viewMode,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        products,
        hasReachedMax,
        currentPage,
        searchQuery,
        isSearchMode,
        categoryId,
        sortBy,
        sortOrder,
        filter,
        viewMode,
        errorMessage,
      ];

  @override
  String toString() => 'ProductListState('
      'status: $status, '
      'productsCount: ${products.length}, '
      'hasReachedMax: $hasReachedMax, '
      'currentPage: $currentPage, '
      'searchQuery: $searchQuery, '
      'isSearchMode: $isSearchMode, '
      'categoryId: $categoryId, '
      'sortBy: $sortBy, '
      'sortOrder: $sortOrder, '
      'viewMode: $viewMode)';
}

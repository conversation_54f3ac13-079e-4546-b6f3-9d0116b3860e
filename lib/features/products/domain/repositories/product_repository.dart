import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../entities/product.dart';
import '../entities/product_review.dart';
import '../value_objects/product_category.dart';

/// Repository interface for product operations
abstract class ProductRepository {
  /// Get products with pagination and filtering
  Future<Either<Failure, List<Product>>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? query,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Get a single product by ID
  Future<Either<Failure, Product>> getProduct(String productId);

  /// Search products
  Future<Either<Failure, List<Product>>> searchProducts({
    required String query,
    int page = 1,
    int limit = 20,
    String? categoryId,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Get products by category
  Future<Either<Failure, List<Product>>> getProductsByCategory({
    required String categoryId,
    int page = 1,
    int limit = 20,
    ProductSortBy? sortBy,
    SortOrder sortOrder = SortOrder.asc,
    ProductFilter? filter,
  });

  /// Get featured products
  Future<Either<Failure, List<Product>>> getFeaturedProducts({
    int page = 1,
    int limit = 20,
  });

  /// Get related products
  Future<Either<Failure, List<Product>>> getRelatedProducts({
    required String productId,
    int limit = 10,
  });

  /// Get product recommendations
  Future<Either<Failure, List<Product>>> getRecommendedProducts({
    String? userId,
    String? productId,
    int limit = 10,
  });

  /// Get products by IDs
  Future<Either<Failure, List<Product>>> getProductsByIds(List<String> productIds);

  /// Get product categories
  Future<Either<Failure, List<ProductCategory>>> getCategories({
    int page = 1,
    int limit = 50,
    String? parentId,
  });

  /// Get category by ID
  Future<Either<Failure, ProductCategory>> getCategory(String categoryId);

  /// Get product reviews
  Future<Either<Failure, List<ProductReview>>> getProductReviews({
    required String productId,
    int page = 1,
    int limit = 20,
    ReviewSortBy? sortBy,
    SortOrder sortOrder = SortOrder.desc,
  });

  /// Add product to favorites/wishlist
  Future<Either<Failure, void>> addToFavorites(String productId);

  /// Remove product from favorites/wishlist
  Future<Either<Failure, void>> removeFromFavorites(String productId);

  /// Get favorite products
  Future<Either<Failure, List<Product>>> getFavoriteProducts({
    int page = 1,
    int limit = 20,
  });

  /// Check if product is in favorites
  Future<Either<Failure, bool>> isProductFavorite(String productId);

  /// Get recently viewed products
  Future<Either<Failure, List<Product>>> getRecentlyViewedProducts({
    int limit = 10,
  });

  /// Add product to recently viewed
  Future<Either<Failure, void>> addToRecentlyViewed(String productId);

  /// Clear recently viewed products
  Future<Either<Failure, void>> clearRecentlyViewed();
}

/// Product sorting options
enum ProductSortBy {
  name,
  price,
  createdAt,
  updatedAt,
  popularity,
  rating,
  featured,
}

/// Review sorting options
enum ReviewSortBy {
  createdAt,
  rating,
  helpfulness,
}

/// Product filtering options
class ProductFilter {
  const ProductFilter({
    this.minPrice,
    this.maxPrice,
    this.brands,
    this.tags,
    this.inStock,
    this.onSale,
    this.featured,
    this.minRating,
    this.hasReviews,
    this.isDigital,
    this.freeShipping,
  });

  final double? minPrice;
  final double? maxPrice;
  final List<String>? brands;
  final List<String>? tags;
  final bool? inStock;
  final bool? onSale;
  final bool? featured;
  final double? minRating;
  final bool? hasReviews;
  final bool? isDigital;
  final bool? freeShipping;

  /// Check if filter is empty
  bool get isEmpty =>
      minPrice == null &&
      maxPrice == null &&
      (brands?.isEmpty ?? true) &&
      (tags?.isEmpty ?? true) &&
      inStock == null &&
      onSale == null &&
      featured == null &&
      minRating == null &&
      hasReviews == null &&
      isDigital == null &&
      freeShipping == null;

  /// Create a copy with modified values
  ProductFilter copyWith({
    double? minPrice,
    double? maxPrice,
    List<String>? brands,
    List<String>? tags,
    bool? inStock,
    bool? onSale,
    bool? featured,
    double? minRating,
    bool? hasReviews,
    bool? isDigital,
    bool? freeShipping,
  }) {
    return ProductFilter(
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      brands: brands ?? this.brands,
      tags: tags ?? this.tags,
      inStock: inStock ?? this.inStock,
      onSale: onSale ?? this.onSale,
      featured: featured ?? this.featured,
      minRating: minRating ?? this.minRating,
      hasReviews: hasReviews ?? this.hasReviews,
      isDigital: isDigital ?? this.isDigital,
      freeShipping: freeShipping ?? this.freeShipping,
    );
  }

  /// Convert to map for API calls
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};

    if (minPrice != null) map['min_price'] = minPrice;
    if (maxPrice != null) map['max_price'] = maxPrice;
    if (brands?.isNotEmpty == true) map['brands'] = brands;
    if (tags?.isNotEmpty == true) map['tags'] = tags;
    if (inStock != null) map['in_stock'] = inStock;
    if (onSale != null) map['on_sale'] = onSale;
    if (featured != null) map['featured'] = featured;
    if (minRating != null) map['min_rating'] = minRating;
    if (hasReviews != null) map['has_reviews'] = hasReviews;
    if (isDigital != null) map['is_digital'] = isDigital;
    if (freeShipping != null) map['free_shipping'] = freeShipping;

    return map;
  }

  @override
  String toString() => 'ProductFilter(${toMap()})';
}

part of 'product_details_bloc.dart';

abstract class ProductDetailsEvent extends Equatable {
  const ProductDetailsEvent();

  @override
  List<Object?> get props => [];
}

/// Event to request product details
class ProductDetailsRequested extends ProductDetailsEvent {
  const ProductDetailsRequested({required this.productId});

  final String productId;

  @override
  List<Object?> get props => [productId];
}

/// Event to select a product variant
class ProductVariantSelected extends ProductDetailsEvent {
  const ProductVariantSelected({required this.variantId});

  final String variantId;

  @override
  List<Object?> get props => [variantId];
}

/// Event to change product quantity
class ProductQuantityChanged extends ProductDetailsEvent {
  const ProductQuantityChanged({required this.quantity});

  final int quantity;

  @override
  List<Object?> get props => [quantity];
}

/// Event to select a product image
class ProductImageSelected extends ProductDetailsEvent {
  const ProductImageSelected({required this.index});

  final int index;

  @override
  List<Object?> get props => [index];
}

/// Event to toggle product favorite status
class ProductFavoriteToggled extends ProductDetailsEvent {
  const ProductFavoriteToggled();
}

/// Event to refresh product details
class ProductDetailsRefreshed extends ProductDetailsEvent {
  const ProductDetailsRefreshed();
}

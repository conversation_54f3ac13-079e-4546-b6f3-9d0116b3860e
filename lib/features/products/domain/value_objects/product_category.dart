import 'package:equatable/equatable.dart';

/// Value object representing a product category
class ProductCategory extends Equatable {
  const ProductCategory({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.parentId,
    this.imageUrl,
    this.sortOrder = 0,
    this.isActive = true,
  });

  final String id;
  final String name;
  final String slug;
  final String? description;
  final String? parentId;
  final String? imageUrl;
  final int sortOrder;
  final bool isActive;

  /// Check if this is a root category (no parent)
  bool get isRoot => parentId == null || parentId!.isEmpty;

  /// Check if this is a subcategory
  bool get isSubcategory => !isRoot;

  /// Create a copy with modified values
  ProductCategory copyWith({
    String? id,
    String? name,
    String? slug,
    String? description,
    String? parentId,
    String? imageUrl,
    int? sortOrder,
    bool? isActive,
  }) {
    return ProductCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      slug: slug ?? this.slug,
      description: description ?? this.description,
      parentId: parentId ?? this.parentId,
      imageUrl: imageUrl ?? this.imageUrl,
      sortOrder: sortOrder ?? this.sortOrder,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        slug,
        description,
        parentId,
        imageUrl,
        sortOrder,
        isActive,
      ];

  @override
  String toString() => 'ProductCategory(id: $id, name: $name, slug: $slug)';
}

import 'package:equatable/equatable.dart';

/// Value object representing monetary amounts
class Money extends Equatable {
  const Money({
    required this.amount,
    required this.currency,
  });

  /// Create Money from double amount
  const Money.fromDouble(double amount, String currency)
      : this(amount: amount, currency: currency);

  /// Create Money from integer cents (e.g., 1000 cents = $10.00)
  Money.fromCents(int cents, String currency)
      : this(amount: cents / 100.0, currency: currency);

  /// Create zero money
  const Money.zero(String currency) : this(amount: 0.0, currency: currency);

  final double amount;
  final String currency;

  /// Get amount in cents (useful for precise calculations)
  int get amountInCents => (amount * 100).round();

  /// Check if amount is zero
  bool get isZero => amount == 0.0;

  /// Check if amount is positive
  bool get isPositive => amount > 0.0;

  /// Check if amount is negative
  bool get isNegative => amount < 0.0;

  /// Add two money amounts (must have same currency)
  Money operator +(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot add different currencies: $currency and ${other.currency}');
    }
    return Money(amount: amount + other.amount, currency: currency);
  }

  /// Subtract two money amounts (must have same currency)
  Money operator -(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot subtract different currencies: $currency and ${other.currency}');
    }
    return Money(amount: amount - other.amount, currency: currency);
  }

  /// Multiply money by a factor
  Money operator *(double factor) {
    return Money(amount: amount * factor, currency: currency);
  }

  /// Divide money by a factor
  Money operator /(double factor) {
    if (factor == 0) {
      throw ArgumentError('Cannot divide by zero');
    }
    return Money(amount: amount / factor, currency: currency);
  }

  /// Compare two money amounts (must have same currency)
  bool operator >(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot compare different currencies: $currency and ${other.currency}');
    }
    return amount > other.amount;
  }

  /// Compare two money amounts (must have same currency)
  bool operator <(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot compare different currencies: $currency and ${other.currency}');
    }
    return amount < other.amount;
  }

  /// Compare two money amounts (must have same currency)
  bool operator >=(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot compare different currencies: $currency and ${other.currency}');
    }
    return amount >= other.amount;
  }

  /// Compare two money amounts (must have same currency)
  bool operator <=(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot compare different currencies: $currency and ${other.currency}');
    }
    return amount <= other.amount;
  }

  /// Format money for display
  String format({
    int decimalPlaces = 2,
    String? symbol,
    bool showSymbol = true,
  }) {
    final formattedAmount = amount.toStringAsFixed(decimalPlaces);
    
    if (!showSymbol) {
      return formattedAmount;
    }

    final currencySymbol = symbol ?? _getCurrencySymbol(currency);
    return '$currencySymbol$formattedAmount';
  }

  /// Get currency symbol for common currencies
  String _getCurrencySymbol(String currencyCode) {
    switch (currencyCode.toUpperCase()) {
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'VND':
        return '₫';
      default:
        return '$currencyCode ';
    }
  }

  /// Create a copy with modified values
  Money copyWith({
    double? amount,
    String? currency,
  }) {
    return Money(
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
    );
  }

  @override
  List<Object?> get props => [amount, currency];

  @override
  String toString() => format();
}

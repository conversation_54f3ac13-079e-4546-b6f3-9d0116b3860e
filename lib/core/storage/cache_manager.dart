import 'dart:convert';
import 'package:injectable/injectable.dart';

/// Interface for cache management
abstract class CacheManager {
  /// Initialize the cache
  Future<void> init();

  /// Cache data with a key and optional expiration
  Future<void> put<T>(
    String key,
    T data, {
    Duration? expiration,
  });

  /// Cache data with a key and optional duration (alias for put)
  Future<void> set<T>(
    String key,
    T data, {
    Duration? duration,
  });

  /// Retrieve cached data by key
  Future<T?> get<T>(String key);

  /// Check if cached data exists and is not expired
  Future<bool> has(String key);

  /// Check if cached data exists (alias for has)
  Future<bool> exists(String key);

  /// Remove cached data by key
  Future<void> remove(String key);

  /// Remove cached data by key (alias for remove)
  Future<void> delete(String key);

  /// Clear all cached data
  Future<void> clear();

  /// Clear expired cache entries
  Future<void> clearExpired();

  /// Get cache size in bytes
  Future<int> getSize();
}

/// Cache entry with expiration support
class CacheEntry {
  const CacheEntry({
    required this.data,
    required this.timestamp,
    this.expiration,
  });

  final String data;
  final DateTime timestamp;
  final Duration? expiration;

  bool get isExpired {
    if (expiration == null) return false;
    return DateTime.now().difference(timestamp) > expiration!;
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'expiration': expiration?.inMilliseconds,
    };
  }

  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      data: json['data'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      expiration: json['expiration'] != null
          ? Duration(milliseconds: json['expiration'] as int)
          : null,
    );
  }
}

/// Hive-based cache manager implementation
@LazySingleton(as: CacheManager)
class HiveCacheManager implements CacheManager {
  // Implementation will be added after setting up Hive

  @override
  Future<void> init() async {
    // TODO: Initialize Hive and open cache box
  }

  @override
  Future<void> put<T>(
    String key,
    T data, {
    Duration? expiration,
  }) async {
    // TODO: Implement using Hive
    final _ = CacheEntry(
      data: jsonEncode(data),
      timestamp: DateTime.now(),
      expiration: expiration,
    );
    // Store entry in Hive box
  }

  @override
  Future<T?> get<T>(String key) async {
    // TODO: Implement using Hive
    // Retrieve entry from Hive box
    // Check if expired
    // Return decoded data
    return null;
  }

  @override
  Future<bool> has(String key) async {
    // TODO: Implement using Hive
    // Check if key exists and is not expired
    return false;
  }

  @override
  Future<void> remove(String key) async {
    // TODO: Implement using Hive
  }

  @override
  Future<void> clear() async {
    // TODO: Implement using Hive
  }

  @override
  Future<void> clearExpired() async {
    // TODO: Implement using Hive
    // Iterate through all entries and remove expired ones
  }

  @override
  Future<int> getSize() async {
    // TODO: Implement using Hive
    // Calculate total size of cached data
    return 0;
  }
}

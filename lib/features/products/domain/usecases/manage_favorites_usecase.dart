import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../entities/product.dart';
import '../repositories/product_repository.dart';

/// Use case for adding product to favorites
@injectable
class AddToFavoritesUseCase implements UseCase<void, AddToFavoritesParams> {
  const AddToFavoritesUseCase(this._repository);

  final ProductRepository _repository;

  @override
  Future<Either<Failure, void>> call(AddToFavoritesParams params) async {
    return await _repository.addToFavorites(params.productId);
  }
}

/// Use case for removing product from favorites
@injectable
class RemoveFromFavoritesUseCase implements UseCase<void, RemoveFromFavoritesParams> {
  const RemoveFromFavoritesUseCase(this._repository);

  final ProductRepository _repository;

  @override
  Future<Either<Failure, void>> call(RemoveFromFavoritesParams params) async {
    return await _repository.removeFromFavorites(params.productId);
  }
}

/// Use case for getting favorite products
@injectable
class GetFavoriteProductsUseCase implements UseCase<List<Product>, GetFavoriteProductsParams> {
  const GetFavoriteProductsUseCase(this._repository);

  final ProductRepository _repository;

  @override
  Future<Either<Failure, List<Product>>> call(GetFavoriteProductsParams params) async {
    return await _repository.getFavoriteProducts(
      page: params.page,
      limit: params.limit,
    );
  }
}

/// Use case for checking if product is favorite
@injectable
class IsProductFavoriteUseCase implements UseCase<bool, IsProductFavoriteParams> {
  const IsProductFavoriteUseCase(this._repository);

  final ProductRepository _repository;

  @override
  Future<Either<Failure, bool>> call(IsProductFavoriteParams params) async {
    return await _repository.isProductFavorite(params.productId);
  }
}

/// Parameters for adding to favorites
class AddToFavoritesParams extends UseCaseParams {
  const AddToFavoritesParams({required this.productId});

  final String productId;

  @override
  String toString() => 'AddToFavoritesParams(productId: $productId)';
}

/// Parameters for removing from favorites
class RemoveFromFavoritesParams extends UseCaseParams {
  const RemoveFromFavoritesParams({required this.productId});

  final String productId;

  @override
  String toString() => 'RemoveFromFavoritesParams(productId: $productId)';
}

/// Parameters for getting favorite products
class GetFavoriteProductsParams extends PaginationParams {
  const GetFavoriteProductsParams({
    super.page = 1,
    super.limit = 20,
  });

  @override
  String toString() => 'GetFavoriteProductsParams(page: $page, limit: $limit)';
}

/// Parameters for checking if product is favorite
class IsProductFavoriteParams extends UseCaseParams {
  const IsProductFavoriteParams({required this.productId});

  final String productId;

  @override
  String toString() => 'IsProductFavoriteParams(productId: $productId)';
}
